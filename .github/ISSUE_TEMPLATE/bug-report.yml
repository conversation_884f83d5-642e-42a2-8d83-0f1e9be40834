name: 🐞 Bug report
description: Create a report to help us improve
labels: [🐞 bug]
title: 'bug: '
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
        This form is only for submitting bug reports. If you have a usage question
        or are unsure if this is really a bug, make sure to:

        - Ask on [GitHub Discussions](https://github.com/orgs/weme-ui/discussions)
  - type: textarea
    id: bug-env
    attributes:
      label: Environment
      description: Output of `npx envinfo --system --npmPackages vue,vite,reka-ui,nuxt,typescript,@weme-ui/weme-ui --binaries --browsers`
      placeholder: Environment
    validations:
      required: true
  - type: textarea
    id: bug-description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is. If you intend to submit a PR for this issue, tell us in the description. Thanks!
      placeholder: Bug description
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: Reproduction
      description: A link to a repo that can reproduce the problem you ran into.
      placeholder: Reproduction
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: If applicable, add any other context about the problem here
  - type: textarea
    id: logs
    attributes:
      label: Logs
      description: |
        Optional if provided reproduction. Please try not to insert an image but copy paste the log text.
      render: shell-script
