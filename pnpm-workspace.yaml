packages:
  - docs
  - registry/*
  - packages/*

catalogs:
  build:
    '@commitlint/cli': ^20.1.0
    '@commitlint/config-conventional': ^20.0.0
    '@types/node': 24.0.15
    '@typescript/native-preview': ^7.0.0-dev.20251008.1
    bumpp: ^10.3.1
    commitizen: ^4.3.1
    conventional-changelog: ^7.1.1
    cz-git: ^1.12.0
    czg: ^1.12.0
    simple-git-hooks: ^2.13.1
    taze: ^19.7.0
    tsdown: ^0.15.6
    typescript: ^5.9.3
    vite: ^7.1.9
  dev:
    '@nuxt/devtools': ^2.6.5
    '@nuxt/icon': ^2.0.0
    '@nuxt/image': ^1.11.0
    '@nuxt/kit': 4.0.3
    '@radix-ui/colors': ^3.0.0
    '@unocss/nuxt': ^66.5.2
    '@unocss/rule-utils': ^66.5.2
    '@vueuse/core': ^13.9.0
    '@vueuse/nuxt': ^13.9.0
    '@weme-ui/schema': ^0.1.5
    bezier-easing: ^2.1.0
    clsx: ^2.1.1
    colorjs.io: ^0.5.2
    defu: ^6.1.4
    motion-v: ^1.7.2
    reka-ui: ^2.5.1
    tailwind-merge: ^3.3.1
    tailwind-variants: ^3.1.1
    ufo: ^1.6.1
    unocss: ^66.5.2
    vite-plugin-vue-devtools: ^8.0.2
  docs:
    '@nuxt/content': ^3.7.1
    '@nuxtjs/i18n': ^10.1.0
    better-sqlite3: ^12.4.1
  icon:
    '@iconify-json/catppuccin': ^1.2.17
    '@iconify-json/logos': ^1.2.9
    '@iconify-json/lucide': ^1.2.69
    '@iconify-json/mingcute': ^1.2.5
    '@iconify-json/simple-icons': ^1.2.54
  lint:
    '@antfu/eslint-config': ^5.4.1
    eslint: ^9.37.0
    eslint-plugin-pnpm: ^1.2.0
    jsonc-eslint-parser: ^2.4.1
    lint-staged: ^16.2.3
    yaml-eslint-parser: ^1.3.0
  nuxt:
    '@nuxt/module-builder': ^1.0.2
    '@nuxt/schema': ^4.1.3
    nuxt: ^4.1.3
  testing:
    '@nuxt/test-utils': ^3.19.2
    '@vue/test-utils': ^2.4.6
    happy-dom: ^19.0.2
    playwright-core: ^1.56.0
    vitest: ^3.2.4
  utils:
    citty: ^0.1.6
    consola: ^3.4.2
    debug: ^4.4.3
    diff: ^8.0.2
    esno: ^4.8.0
    execa: ^9.6.0
    fast-glob: ^3.3.3
    giget: ^2.0.0
    handlebars: ^4.7.8
    mkdirp: ^3.0.1
    nypm: ^0.6.2
    pathe: 2.0.3
    pkg-types: ^2.3.0
    scule: ^1.3.0
    zod: ^4.1.12
  vue:
    '@vitejs/plugin-vue': ^6.0.1
    vue: ^3.5.22
    vue-router: ^4.5.1
    vue-tsc: ^3.1.1

onlyBuiltDependencies:
  - '@parcel/watcher'
  - better-sqlite3
  - esbuild
  - sharp
  - simple-git-hooks
  - vue-demi
