{"name": "@weme-ui/colors", "type": "module", "version": "0.0.0", "packageManager": "pnpm@10.18.1", "description": "Weme UI - Colors", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/weme-ui/weme-ui#readme", "repository": {"type": "git", "url": "git+https://github.com/weme-ui/weme-ui.git", "directory": "packages/colors"}, "bugs": {"url": "https://github.com/weme-ui/weme-ui/issues"}, "keywords": ["weme-ui", "weme-ui-colors", "radix-colors", "radix-colors-generator"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "scripts": {"build": "tsdown", "dev": "tsdown --watch", "dev:prepare": "pnpm run build", "prepublishOnly": "pnpm run build", "test": "vitest", "typecheck": "tsgo --noEmit"}, "peerDependencies": {"@radix-ui/colors": "catalog:dev", "bezier-easing": "catalog:dev", "colorjs.io": "catalog:dev"}, "devDependencies": {"@radix-ui/colors": "catalog:dev", "@types/node": "catalog:build", "bezier-easing": "catalog:dev", "bumpp": "catalog:build", "colorjs.io": "catalog:dev", "tsdown": "catalog:build", "typescript": "catalog:build", "vitest": "catalog:testing"}}