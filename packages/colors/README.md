<p align="center">
  <img align="center" src="https://raw.githubusercontent.com/moujinet/assets/main/weme-ui/png/circle-128.png" height="128" />
  <h1 align="center">
    Weme UI <sup style="color: #4CBBA5">colors</sup>
  </h1>
</p>

[![npm version][npm-version-src]][npm-version-href]
[![License][license-src]][license-href]

## 特性

- 🎨 [Radix Colors](https://www.radix-ui.com/colors) 基于 Radix Colors 并提供便捷的自定义颜色

## 如何开始

```shell
pnpm i -D @weme-ui/colors
```

## 许可证

[MIT][license-href] License © 2025 [weme-ui][github-href]

[github-href]: https://github.com/weme-ui/weme-ui
[npm-version-src]: https://img.shields.io/npm/v/@weme-ui/colors?style=flat&colorA=1d2129&colorB=4CBBA5
[npm-version-href]: https://npmjs.com/package/@weme-ui/colors
[license-src]: https://img.shields.io/github/license/weme-ui/weme-ui.svg?style=flat&colorA=1d2129&colorB=4CBBA5
[license-href]: https://github.com/weme-ui/weme-ui/blob/main/LICENSE
