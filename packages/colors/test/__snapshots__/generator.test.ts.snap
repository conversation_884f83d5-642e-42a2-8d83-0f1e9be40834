// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`generator > should get color objects 1`] = `
[
  "oklch(99.41% 0.0023 262.6)",
  "oklch(98.27% 0.011 262.6)",
  "oklch(96.06% 0.0233 262.6)",
  "oklch(93.56% 0.0456 262.6)",
  "oklch(90.37% 0.0686 262.6)",
  "oklch(86.31% 0.0944 262.6)",
  "oklch(80.72% 0.1159 262.6)",
  "oklch(73.09% 0.1525 262.6)",
  "oklch(53.32% 0.2596 262.6)",
  "oklch(48.58% 0.2366 262.6)",
  "oklch(51.8% 0.2366 262.6)",
  "oklch(31.46% 0.1181 262.6)",
]
`;

exports[`generator > should get color objects 2`] = `
[
  "oklch(17.76% 0 none)",
  "oklch(21.34% 0 none)",
  "oklch(25.38% 0 none)",
  "oklch(28.33% 0 none)",
  "oklch(31.3% 0 none)",
  "oklch(34.91% 0 none)",
  "oklch(40.22% 0 none)",
  "oklch(48.8% 0 none)",
  "oklch(59.99% 0 none)",
  "oklch(55.7% 0 none)",
  "oklch(77% 0 none)",
  "oklch(94.89% 0 none)",
]
`;
