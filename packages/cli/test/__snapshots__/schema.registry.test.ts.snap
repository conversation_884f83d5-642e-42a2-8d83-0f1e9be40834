// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`registry schema > should append registry item 1`] = `
{
  "access": "public",
  "dependencies": [
    "clsx",
    "tailwind-merge",
    "tailwind-variants",
  ],
  "description": "The standard components for Weme UI.",
  "devDependencies": [
    "@nuxt/icon",
    "@unocss/nuxt",
    "@vueuse/core",
    "@weme-ui/nuxt",
    "@weme-ui/unocss-preset",
    "reka-ui",
    "unocss",
    "vue",
  ],
  "directory": "registry/std",
  "items": [
    {
      "categories": [
        "general",
      ],
      "description": "A component to display any icon from Iconify.",
      "files": [
        {
          "path": "components/icon/icon.vue",
          "type": "component",
        },
        {
          "path": "components/icon/icon.props.ts",
          "type": "type",
        },
        {
          "path": "components/icon/icon-box.vue",
          "type": "component",
        },
        {
          "path": "components/icon/icon-box.props.ts",
          "type": "type",
        },
        {
          "path": "components/icon/icon-box.style.ts",
          "type": "style",
        },
        {
          "path": "components/icon/icon-test.vue",
          "type": "component",
        },
      ],
      "name": "icon",
      "title": "Icon",
      "type": "component",
    },
    {
      "categories": [
        "general",
      ],
      "description": "The test component.",
      "files": [
        {
          "path": "components/test/test.vue",
          "type": "component",
        },
      ],
      "name": "test",
      "title": "test",
      "type": "component",
    },
  ],
  "meta": {
    "authors": [
      "Allen Luo <<EMAIL>>",
    ],
    "bugs": "https://github.com/weme-ui/weme-ui/issues",
    "homepage": "https://github.com/weme-ui/weme-ui",
    "repository": "https://github.com/weme-ui/weme-ui",
  },
  "name": "weme-ui/std",
  "prefix": "ui",
  "version": "0.0.0",
}
`;

exports[`registry schema > should append registry item files 1`] = `
{
  "access": "public",
  "dependencies": [
    "clsx",
    "tailwind-merge",
    "tailwind-variants",
  ],
  "description": "The standard components for Weme UI.",
  "devDependencies": [
    "@nuxt/icon",
    "@unocss/nuxt",
    "@vueuse/core",
    "@weme-ui/nuxt",
    "@weme-ui/unocss-preset",
    "reka-ui",
    "unocss",
    "vue",
  ],
  "directory": "registry/std",
  "items": [
    {
      "categories": [
        "general",
      ],
      "description": "A component to display any icon from Iconify.",
      "files": [
        {
          "path": "components/icon/icon.vue",
          "type": "component",
        },
        {
          "path": "components/icon/icon.props.ts",
          "type": "type",
        },
        {
          "path": "components/icon/icon-box.vue",
          "type": "component",
        },
        {
          "path": "components/icon/icon-box.props.ts",
          "type": "type",
        },
        {
          "path": "components/icon/icon-box.style.ts",
          "type": "style",
        },
        {
          "path": "components/icon/icon-test.vue",
          "type": "component",
        },
      ],
      "name": "icon",
      "title": "Icon",
      "type": "component",
    },
    {
      "categories": [
        "general",
      ],
      "description": "The test component.",
      "files": [
        {
          "path": "components/test/test.vue",
          "type": "component",
        },
      ],
      "name": "test",
      "title": "test",
      "type": "component",
    },
  ],
  "meta": {
    "authors": [
      "Allen Luo <<EMAIL>>",
    ],
    "bugs": "https://github.com/weme-ui/weme-ui/issues",
    "homepage": "https://github.com/weme-ui/weme-ui",
    "repository": "https://github.com/weme-ui/weme-ui",
  },
  "name": "weme-ui/std",
  "prefix": "ui",
  "version": "0.0.0",
}
`;

exports[`registry schema > should get registry info 1`] = `
{
  "access": "public",
  "authors": [
    "Allen Luo <<EMAIL>>",
  ],
  "bugs": "https://github.com/weme-ui/weme-ui/issues",
  "dependencies": 3,
  "description": "The standard components for Weme UI.",
  "devDependencies": 8,
  "directory": "registry/std",
  "homepage": "https://github.com/weme-ui/weme-ui",
  "items": 2,
  "name": "weme-ui/std",
  "prefix": "ui",
  "repository": "https://github.com/weme-ui/weme-ui",
  "version": "0.0.0",
}
`;

exports[`registry schema > should get registry item 1`] = `
{
  "categories": [
    "general",
  ],
  "description": "A component to display any icon from Iconify.",
  "files": [
    {
      "path": "components/icon/icon.vue",
      "type": "component",
    },
    {
      "path": "components/icon/icon.props.ts",
      "type": "type",
    },
    {
      "path": "components/icon/icon-box.vue",
      "type": "component",
    },
    {
      "path": "components/icon/icon-box.props.ts",
      "type": "type",
    },
    {
      "path": "components/icon/icon-box.style.ts",
      "type": "style",
    },
    {
      "path": "components/icon/icon-test.vue",
      "type": "component",
    },
  ],
  "name": "icon",
  "title": "Icon",
  "type": "component",
}
`;

exports[`registry schema > should list registries 1`] = `
[
  "test",
]
`;

exports[`registry schema > should load registry config 1`] = `
{
  "access": "public",
  "dependencies": [
    "clsx",
    "tailwind-merge",
    "tailwind-variants",
  ],
  "description": "The standard components for Weme UI.",
  "devDependencies": [
    "@nuxt/icon",
    "@unocss/nuxt",
    "@vueuse/core",
    "@weme-ui/nuxt",
    "@weme-ui/unocss-preset",
    "reka-ui",
    "unocss",
    "vue",
  ],
  "directory": "registry/std",
  "items": [
    {
      "categories": [
        "general",
      ],
      "description": "A component to display any icon from Iconify.",
      "files": [
        {
          "path": "components/icon/icon.vue",
          "type": "component",
        },
        {
          "path": "components/icon/icon.props.ts",
          "type": "type",
        },
        {
          "path": "components/icon/icon-box.vue",
          "type": "component",
        },
        {
          "path": "components/icon/icon-box.props.ts",
          "type": "type",
        },
        {
          "path": "components/icon/icon-box.style.ts",
          "type": "style",
        },
        {
          "path": "components/icon/icon-test.vue",
          "type": "component",
        },
      ],
      "name": "icon",
      "title": "Icon",
      "type": "component",
    },
    {
      "categories": [
        "general",
      ],
      "description": "The test component.",
      "files": [
        {
          "path": "components/test/test.vue",
          "type": "component",
        },
      ],
      "name": "test",
      "title": "test",
      "type": "component",
    },
  ],
  "meta": {
    "authors": [
      "Allen Luo <<EMAIL>>",
    ],
    "bugs": "https://github.com/weme-ui/weme-ui/issues",
    "homepage": "https://github.com/weme-ui/weme-ui",
    "repository": "https://github.com/weme-ui/weme-ui",
  },
  "name": "weme-ui/std",
  "prefix": "ui",
  "version": "0.0.0",
}
`;
