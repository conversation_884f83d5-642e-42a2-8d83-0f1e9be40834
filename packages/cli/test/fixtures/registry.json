{"$schema": "https://weme-ui.github.io/schema/registry.json", "name": "weme-ui/std", "description": "The standard components for Weme UI.", "version": "0.0.0", "prefix": "ui", "directory": "registry/std", "access": "public", "meta": {"authors": ["<PERSON> <<EMAIL>>"], "homepage": "https://github.com/weme-ui/weme-ui", "repository": "https://github.com/weme-ui/weme-ui", "bugs": "https://github.com/weme-ui/weme-ui/issues"}, "dependencies": ["clsx", "tailwind-merge", "tailwind-variants"], "devDependencies": ["@nuxt/icon", "@unocss/nuxt", "@vueuse/core", "@weme-ui/nuxt", "@weme-ui/unocss-preset", "reka-ui", "unocss", "vue"], "items": [{"name": "icon", "type": "component", "title": "Icon", "description": "A component to display any icon from Iconify.", "categories": ["general"], "files": [{"type": "component", "path": "components/icon/icon.vue"}, {"type": "type", "path": "components/icon/icon.props.ts"}, {"type": "component", "path": "components/icon/icon-box.vue"}, {"type": "type", "path": "components/icon/icon-box.props.ts"}, {"type": "style", "path": "components/icon/icon-box.style.ts"}]}]}