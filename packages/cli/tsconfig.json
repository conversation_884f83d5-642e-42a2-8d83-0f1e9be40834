{"compilerOptions": {"target": "esnext", "lib": ["esnext"], "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "strict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "declaration": true, "declarationMap": true, "sourceMap": true, "esModuleInterop": true, "isolatedModules": true, "skipLibCheck": true}, "exclude": ["**/dist/**", "**/node_modules/**", "**/test/**", "tsdown.config.ts"]}