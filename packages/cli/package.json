{"name": "@weme-ui/weme-ui", "type": "module", "version": "0.0.0", "packageManager": "pnpm@10.18.1", "description": "Re-usable UI components with Reka UI and UnoCSS.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/weme-ui/weme-ui", "repository": {"type": "git", "url": "git+https://github.com/weme-ui/weme-ui.git", "directory": "packages/cli"}, "bugs": {"url": "https://github.com/weme-ui/weme-ui/issues"}, "keywords": ["weme-ui", "unocss", "ui-components", "re-usable-ui-components", "unocss-ui-components", "reka-ui-components", "weme-ui-components"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "bin": "./dist/index.js", "files": ["dist", "package.json"], "scripts": {"build": "tsdown", "dev": "tsdown --watch", "dev:prepare": "pnpm run build", "prepublishOnly": "pnpm run build", "test": "vitest", "typecheck": "tsgo --noEmit"}, "dependencies": {"@weme-ui/schema": "catalog:dev", "citty": "catalog:utils", "consola": "catalog:utils", "debug": "catalog:utils", "diff": "catalog:utils", "execa": "catalog:utils", "fast-glob": "catalog:utils", "giget": "catalog:utils", "handlebars": "catalog:utils", "mkdirp": "catalog:utils", "nypm": "catalog:utils", "pathe": "catalog:utils", "pkg-types": "catalog:utils", "scule": "catalog:utils", "zod": "catalog:utils"}, "devDependencies": {"@types/node": "catalog:build", "bumpp": "catalog:build", "tsdown": "catalog:build", "typescript": "catalog:build", "vitest": "catalog:testing"}}