{"name": "@weme-ui/nuxt", "type": "module", "version": "0.0.0", "packageManager": "pnpm@10.18.1", "description": "Weme UI - Nuxt module.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/weme-ui/weme-ui#readme", "repository": {"type": "git", "url": "git+https://github.com/weme-ui/weme-ui.git", "directory": "packages/nuxt"}, "bugs": {"url": "https://github.com/weme-ui/weme-ui/issues"}, "exports": {".": {"types": "./dist/types.d.mts", "import": "./dist/module.mjs"}}, "main": "./dist/module.mjs", "typesVersions": {"*": {".": ["./dist/types.d.mts"]}}, "files": ["dist"], "scripts": {"build": "nuxt-module-build build", "stub": "nuxt-module-build build --stub", "dev": "pnpm run dev:prepare && nuxi dev playground", "dev:build": "nuxi build playground", "dev:prepare": "pnpm run build --stub && nuxt-module-build prepare && nuxi prepare playground", "prepack": "pnpm run build", "prepublishOnly": "pnpm run build", "typecheck": "vue-tsc --noEmit"}, "peerDependencies": {"defu": "catalog:dev", "fast-glob": "catalog:utils"}, "dependencies": {"@weme-ui/colors": "workspace:*", "@weme-ui/schema": "catalog:dev", "@weme-ui/unocss-preset": "workspace:*", "unocss": "catalog:dev"}, "devDependencies": {"@iconify-json/lucide": "catalog:icon", "@iconify-json/mingcute": "catalog:icon", "@nuxt/devtools": "catalog:dev", "@nuxt/icon": "catalog:dev", "@nuxt/image": "catalog:dev", "@nuxt/kit": "catalog:dev", "@nuxt/module-builder": "catalog:nuxt", "@nuxt/schema": "catalog:nuxt", "@types/node": "catalog:build", "@unocss/nuxt": "catalog:dev", "@vueuse/nuxt": "catalog:dev", "defu": "catalog:dev", "fast-glob": "catalog:utils", "nuxt": "catalog:nuxt", "typescript": "catalog:build", "unocss": "catalog:dev", "vue-tsc": "catalog:vue"}}