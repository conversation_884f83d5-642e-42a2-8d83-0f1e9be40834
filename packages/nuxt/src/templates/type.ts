import type { NuxtTypeTemplate } from 'nuxt/schema'
import type { WemeTheme } from '../runtime/types'
import { colors } from '@weme-ui/unocss-preset'

export function createWemeType(
  themes: WemeTheme[],
  accentColors: string[],
  neutralColors: string[],
): NuxtTypeTemplate {
  const themeNames = themes.filter(t => t.id !== 'ROOT').map(t => `'${t.id}'`).join(' | ')
  const accentColorNames = accentColors.map(c => `'${c}'`).join(' | ')
  const neutralColorNames = neutralColors.map(c => `'${c}'`).join(' | ')

  return {
    filename: 'types/weme.d.ts',
    getContents() {
      return `// GENERATED BY @weme-ui/nuxt
declare type AvailableThemes = ${themeNames || 'string'}
declare type WemeThemeColor = ${colors.map(c => `'${c}'`).join(' | ')}
declare type WemeAccentColor = ${accentColorNames || 'string'}
declare type WemeNeutralColor = ${neutralColorNames || 'string'}
`
    },
  }
}
