import type { NuxtTemplate } from 'nuxt/schema'
import type { WemeTheme } from '../runtime/types'

export function createUnoCssConfig(
  variablePrefix: string,
  accentColors?: Record<string, string>,
  neutralColors?: Record<string, string>,
  themes?: WemeTheme[],
  cssVars?: Record<string, any>,
): NuxtTemplate {
  return {
    filename: 'uno.config.ts',
    getContents() {
      return `// GENERATED BY @weme-ui/nuxt
import { defineConfig, presetWind4, transformerVariantGroup } from 'unocss'
import { presetWemeUI } from '@weme-ui/unocss-preset'

const accentColors = ${JSON.stringify(accentColors || {}, null, 2)}

const neutralColors = ${JSON.stringify(neutralColors || {}, null, 2)}

const themes = ${JSON.stringify(themes || [], null, 2)}

const cssVars = ${JSON.stringify(cssVars || {}, null, 2)}

export default defineConfig({
  presets: [
    presetWind4({
      arbitraryVariants: true,
    }),

    presetWemeUI({
      variablePrefix: '${variablePrefix}',
      accentColors,
      neutralColors,
      themes,
      cssVars,
    }),
  ],

  transformers: [
    transformerVariantGroup(),
  ],

  content: {
    pipeline: {
      include: [
        /\\.(vue|svelte|[jt]sx|style\\.[jt]s|vine\\.[jt]s|mdx?|astro|elm|php|phtml|html)($|\\?)/,
        /styles?\\.[jt]s($|\\?)/,
      ],
    },
  },
})
`
    },
    write: true,
  }
}
