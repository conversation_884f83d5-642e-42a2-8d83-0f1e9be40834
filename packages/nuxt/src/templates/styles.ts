import type { NuxtTemplate } from 'nuxt/schema'
import { colors, variantsMap } from '@weme-ui/unocss-preset'

export function createWemeStyles(): NuxtTemplate {
  return {
    filename: 'weme/styles.ts',
    getContents() {
      return `// GENERATED BY @weme-ui/nuxt
export type TypeName = ${Object.keys(variantsMap).map(c => `'${c}'`).join(' | ')}
export type ColorName = ${colors.map(c => `'${c}'`).join(' | ')}

type ColorVariantClasses = Record<
  ColorName,
  {
    type: TypeName
    variant: string
    class: string
  }[]
>

export const colorStyles: ColorVariantClasses = {
${colors.map(
  color => (`  ${color}: [
${Object.entries(variantsMap).map(([type, variants]) => {
      function skip(value: string, key: string): string {
        return key === value ? '' : `-${key}`
      }

      return variants.map((variant) => {
        return `    { variant: '${variant}', type: '${type}', class: '${type}-${color}${skip('solid', variant)}' },`
      }).join('\n')
    }).join('\n')}
  ]`),
).join(',\n')}
}
`
    },
    write: true,
  }
}
