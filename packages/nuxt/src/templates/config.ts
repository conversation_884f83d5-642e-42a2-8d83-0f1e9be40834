import type { NuxtTypeTemplate } from 'nuxt/schema'

export function createWemeConfigType(): NuxtTypeTemplate {
  return {
    filename: 'types/weme.config.d.ts',
    getContents() {
      return `// GENERATED BY @weme-ui/nuxt
interface WemeAppConfig {
  theme?: AvailableThemes
  radius?: string
  translucent?: boolean
  accentColor?: WemeAccentColor
  neutralColor?: WemeNeutralColor
  icons?: Partial<Record<string, string>>
}

declare module '@nuxt/schema' {
  interface AppConfigInput {
    weme?: WemeAppConfig
  }

  interface AppConfig {
    weme: Omit<WemeAppConfig, 'icons'> & Required<Pick<WemeAppConfig, 'icons'>>
  }
}

export {}`
    },
  }
}
