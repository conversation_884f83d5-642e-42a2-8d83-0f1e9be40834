import { writeFile } from 'node:fs'
import { getColorNames, transformColor } from '@weme-ui/colors'

function generateRadixColors() {
  const colorNames = getColorNames()

  function invertColor(color: string) {
    if (color === 'black')
      return 'white'
    if (color === 'white')
      return 'black'
    return color
  }

  const colors = `// Generated by @weme-ui/unocss-preset

export default {
${colorNames.map(
    color => (`  ${color}: {
    light: {
${transformColor({ color })
        .map((c, i) => `      ${i + 1}: '${c}',`)
        .join('\n')}
    },
    dark: {
${transformColor({ color: invertColor(color), appearance: 'dark' })
        .map((c, i) => `      ${i + 1}: '${c}',`)
        .join('\n')}
    },
  },`),
  ).join('\n')
}
}
`

  writeFile(
    'src/colors.ts',
    colors,
    { encoding: 'utf8', flag: 'w' },
    (err) => {
      if (err)
        throw err
    },
  )
}

generateRadixColors()
