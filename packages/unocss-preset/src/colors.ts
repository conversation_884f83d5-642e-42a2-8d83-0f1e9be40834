// Generated by @weme-ui/unocss-preset

export default {
  black: {
    light: {
      1: 'oklch(0% 0 none / 0.05)',
      2: 'oklch(0% 0 none / 0.1)',
      3: 'oklch(0% 0 none / 0.15)',
      4: 'oklch(0% 0 none / 0.2)',
      5: 'oklch(0% 0 none / 0.3)',
      6: 'oklch(0% 0 none / 0.4)',
      7: 'oklch(0% 0 none / 0.5)',
      8: 'oklch(0% 0 none / 0.6)',
      9: 'oklch(0% 0 none / 0.7)',
      10: 'oklch(0% 0 none / 0.8)',
      11: 'oklch(0% 0 none / 0.9)',
      12: 'oklch(0% 0 none / 0.95)',
    },
    dark: {
      1: 'oklch(100% 0 none / 0.05)',
      2: 'oklch(100% 0 none / 0.1)',
      3: 'oklch(100% 0 none / 0.15)',
      4: 'oklch(100% 0 none / 0.2)',
      5: 'oklch(100% 0 none / 0.3)',
      6: 'oklch(100% 0 none / 0.4)',
      7: 'oklch(100% 0 none / 0.5)',
      8: 'oklch(100% 0 none / 0.6)',
      9: 'oklch(100% 0 none / 0.7)',
      10: 'oklch(100% 0 none / 0.8)',
      11: 'oklch(100% 0 none / 0.9)',
      12: 'oklch(100% 0 none / 0.95)',
    },
  },
  white: {
    light: {
      1: 'oklch(100% 0 none / 0.05)',
      2: 'oklch(100% 0 none / 0.1)',
      3: 'oklch(100% 0 none / 0.15)',
      4: 'oklch(100% 0 none / 0.2)',
      5: 'oklch(100% 0 none / 0.3)',
      6: 'oklch(100% 0 none / 0.4)',
      7: 'oklch(100% 0 none / 0.5)',
      8: 'oklch(100% 0 none / 0.6)',
      9: 'oklch(100% 0 none / 0.7)',
      10: 'oklch(100% 0 none / 0.8)',
      11: 'oklch(100% 0 none / 0.9)',
      12: 'oklch(100% 0 none / 0.95)',
    },
    dark: {
      1: 'oklch(0% 0 none / 0.05)',
      2: 'oklch(0% 0 none / 0.1)',
      3: 'oklch(0% 0 none / 0.15)',
      4: 'oklch(0% 0 none / 0.2)',
      5: 'oklch(0% 0 none / 0.3)',
      6: 'oklch(0% 0 none / 0.4)',
      7: 'oklch(0% 0 none / 0.5)',
      8: 'oklch(0% 0 none / 0.6)',
      9: 'oklch(0% 0 none / 0.7)',
      10: 'oklch(0% 0 none / 0.8)',
      11: 'oklch(0% 0 none / 0.9)',
      12: 'oklch(0% 0 none / 0.95)',
    },
  },
  gray: {
    light: {
      1: 'oklch(99.09% 0 none)',
      2: 'oklch(98.1% 0 none)',
      3: 'oklch(95.35% 0 none)',
      4: 'oklch(92.96% 0 none)',
      5: 'oklch(90.79% 0 none)',
      6: 'oklch(88.38% 0 none)',
      7: 'oklch(85.08% 0 none)',
      8: 'oklch(79.1% 0 none)',
      9: 'oklch(64.35% 0 none)',
      10: 'oklch(60.85% 0 none)',
      11: 'oklch(50.31% 0 none)',
      12: 'oklch(24.3% 0 none)',
    },
    dark: {
      1: 'oklch(17.8% 0 none)',
      2: 'oklch(21.34% 0 none)',
      3: 'oklch(25.37% 0 none)',
      4: 'oklch(28.32% 0 none)',
      5: 'oklch(31.3% 0 none)',
      6: 'oklch(34.9% 0 none)',
      7: 'oklch(40.23% 0 none)',
      8: 'oklch(48.77% 0 none)',
      9: 'oklch(53.79% 0 none)',
      10: 'oklch(58.43% 0 none)',
      11: 'oklch(77% 0 none)',
      12: 'oklch(94.89% 0 none)',
    },
  },
  mauve: {
    light: {
      1: 'oklch(99.18% 0.0018 321.1)',
      2: 'oklch(98.32% 0.0033 311.2)',
      3: 'oklch(95.56% 0.0059 314.2)',
      4: 'oklch(93.16% 0.0078 310)',
      5: 'oklch(90.9% 0.0101 306.6)',
      6: 'oklch(88.6% 0.0116 304)',
      7: 'oklch(85.36% 0.0143 300.6)',
      8: 'oklch(79.36% 0.0193 293.5)',
      9: 'oklch(64.58% 0.0195 292.6)',
      10: 'oklch(61.06% 0.0185 293.1)',
      11: 'oklch(50.48% 0.016 296.1)',
      12: 'oklch(24.46% 0.0134 298)',
    },
    dark: {
      1: 'oklch(17.99% 0.0043 307.8)',
      2: 'oklch(21.52% 0.0041 307.8)',
      3: 'oklch(25.5% 0.0055 306.5)',
      4: 'oklch(28.45% 0.0076 307.8)',
      5: 'oklch(31.38% 0.009 306.9)',
      6: 'oklch(35.01% 0.0101 304)',
      7: 'oklch(40.16% 0.0122 304.4)',
      8: 'oklch(49.19% 0.0159 300.7)',
      9: 'oklch(54.05% 0.0169 294)',
      10: 'oklch(58.62% 0.0167 295.4)',
      11: 'oklch(76.98% 0.014 296.6)',
      12: 'oklch(94.94% 0.0026 286.3)',
    },
  },
  slate: {
    light: {
      1: 'oklch(99.12% 0.0015 286.4)',
      2: 'oklch(98.24% 0.003 286.3)',
      3: 'oklch(95.57% 0.0045 281.9)',
      4: 'oklch(93.14% 0.006 283)',
      5: 'oklch(91.01% 0.0075 283.6)',
      6: 'oklch(88.76% 0.0091 281.8)',
      7: 'oklch(85.34% 0.0115 280.8)',
      8: 'oklch(79.39% 0.0158 278.1)',
      9: 'oklch(64.56% 0.0162 277.8)',
      10: 'oklch(60.98% 0.0158 273.2)',
      11: 'oklch(50.26% 0.0137 263.4)',
      12: 'oklch(24.13% 0.0098 249.5)',
    },
    dark: {
      1: 'oklch(17.89% 0.004 286)',
      2: 'oklch(21.33% 0.0041 264.4)',
      3: 'oklch(25.34% 0.0057 261.2)',
      4: 'oklch(28.27% 0.0073 259.5)',
      5: 'oklch(31.14% 0.0082 259.7)',
      6: 'oklch(34.59% 0.0099 256.5)',
      7: 'oklch(39.77% 0.012 253.8)',
      8: 'oklch(48.99% 0.0157 247.6)',
      9: 'oklch(53.7% 0.0152 261.1)',
      10: 'oklch(58.3% 0.0142 263.3)',
      11: 'oklch(76.77% 0.0104 261.3)',
      12: 'oklch(94.88% 0.0028 264.6)',
    },
  },
  sage: {
    light: {
      1: 'oklch(99.23% 0.0025 160.4)',
      2: 'oklch(98.08% 0.0027 171.2)',
      3: 'oklch(95.5% 0.0035 170.4)',
      4: 'oklch(93.12% 0.0035 170.4)',
      5: 'oklch(90.92% 0.004 167.7)',
      6: 'oklch(88.51% 0.004 167.7)',
      7: 'oklch(85.11% 0.0044 169.9)',
      8: 'oklch(79.24% 0.0053 169.5)',
      9: 'oklch(63.93% 0.0104 172.4)',
      10: 'oklch(60.48% 0.0097 172.8)',
      11: 'oklch(50.07% 0.0078 175.9)',
      12: 'oklch(23.98% 0.0121 166.8)',
    },
    dark: {
      1: 'oklch(17.92% 0.0036 167.4)',
      2: 'oklch(21.11% 0.0037 160.2)',
      3: 'oklch(25.12% 0.004 164.2)',
      4: 'oklch(28.12% 0.0049 164.9)',
      5: 'oklch(31.07% 0.0052 167.4)',
      6: 'oklch(34.71% 0.0061 167.4)',
      7: 'oklch(39.92% 0.0078 167.4)',
      8: 'oklch(48.82% 0.0091 169.8)',
      9: 'oklch(53.29% 0.0175 170.8)',
      10: 'oklch(57.93% 0.016 170.5)',
      11: 'oklch(76.6% 0.01 167.5)',
      12: 'oklch(94.73% 0.0024 167.7)',
    },
  },
  olive: {
    light: {
      1: 'oklch(99.31% 0.0015 149.1)',
      2: 'oklch(98.3% 0.0033 144.8)',
      3: 'oklch(95.61% 0.0036 141.1)',
      4: 'oklch(93.2% 0.0042 142.1)',
      5: 'oklch(90.95% 0.0045 139.3)',
      6: 'oklch(88.55% 0.0056 141.1)',
      7: 'oklch(85.17% 0.0056 141.1)',
      8: 'oklch(79.21% 0.0071 140.6)',
      9: 'oklch(64.04% 0.0115 136.5)',
      10: 'oklch(60.54% 0.0116 136.5)',
      11: 'oklch(50.01% 0.0111 139.9)',
      12: 'oklch(24.19% 0.0112 138.2)',
    },
    dark: {
      1: 'oklch(17.97% 0.0043 128.3)',
      2: 'oklch(21.17% 0.0041 128.3)',
      3: 'oklch(25.16% 0.0051 129)',
      4: 'oklch(28.07% 0.0056 131.4)',
      5: 'oklch(31.01% 0.0066 131.4)',
      6: 'oklch(34.64% 0.0076 134.4)',
      7: 'oklch(39.86% 0.0096 136)',
      8: 'oklch(48.77% 0.0124 140.8)',
      9: 'oklch(53.52% 0.0176 139.6)',
      10: 'oklch(58.12% 0.0163 140.1)',
      11: 'oklch(76.59% 0.0128 139.5)',
      12: 'oklch(94.7% 0.0034 144.8)',
    },
  },
  sand: {
    light: {
      1: 'oklch(99.37% 0.0011 106.4)',
      2: 'oklch(98.22% 0.0015 106.4)',
      3: 'oklch(95.55% 0.0023 97.61)',
      4: 'oklch(93.22% 0.0034 100.6)',
      5: 'oklch(90.99% 0.0038 95.91)',
      6: 'oklch(88.55% 0.005 98.34)',
      7: 'oklch(85.25% 0.0062 96.6)',
      8: 'oklch(79.18% 0.0083 96.5)',
      9: 'oklch(64.13% 0.0103 106.7)',
      10: 'oklch(60.57% 0.0096 106.7)',
      11: 'oklch(49.79% 0.0079 106.7)',
      12: 'oklch(24.34% 0.0079 96.31)',
    },
    dark: {
      1: 'oklch(17.76% 0.0023 106.6)',
      2: 'oklch(21.29% 0.0022 106.6)',
      3: 'oklch(25.31% 0.0031 106.6)',
      4: 'oklch(28.28% 0.0036 99.02)',
      5: 'oklch(31.24% 0.0044 100.7)',
      6: 'oklch(34.91% 0.0057 102.2)',
      7: 'oklch(40.07% 0.007 96.1)',
      8: 'oklch(49.01% 0.0095 91.73)',
      9: 'oklch(53.46% 0.011 94.07)',
      10: 'oklch(58.11% 0.0107 94.05)',
      11: 'oklch(76.66% 0.0091 97.38)',
      12: 'oklch(94.83% 0.0026 106.4)',
    },
  },
  tomato: {
    light: {
      1: 'oklch(99.35% 0.0031 22.75)',
      2: 'oklch(98.42% 0.0071 31.02)',
      3: 'oklch(95.44% 0.022 30.88)',
      4: 'oklch(92.6% 0.0471 31.76)',
      5: 'oklch(89.21% 0.0626 31.49)',
      6: 'oklch(85.24% 0.077 32.01)',
      7: 'oklch(80.28% 0.0943 32.17)',
      8: 'oklch(74.15% 0.1182 32.07)',
      9: 'oklch(62.7% 0.1937 33.31)',
      10: 'oklch(60.4% 0.1953 33.26)',
      11: 'oklch(56.65% 0.1977 32.81)',
      12: 'oklch(34.61% 0.0798 30.53)',
    },
    dark: {
      1: 'oklch(18.69% 0.0117 19.59)',
      2: 'oklch(20.77% 0.0167 32.02)',
      3: 'oklch(25.37% 0.0544 26.94)',
      4: 'oklch(29.11% 0.087 28.17)',
      5: 'oklch(33.14% 0.0971 28.66)',
      6: 'oklch(38.01% 0.0994 30.01)',
      7: 'oklch(44.74% 0.1068 31.46)',
      8: 'oklch(53.78% 0.1301 32.94)',
      9: 'oklch(62.7% 0.1937 33.31)',
      10: 'oklch(66.5% 0.1791 34.15)',
      11: 'oklch(78.43% 0.1636 36.02)',
      12: 'oklch(89.86% 0.0466 31.11)',
    },
  },
  red: {
    light: {
      1: 'oklch(99.35% 0.0031 22.75)',
      2: 'oklch(98.32% 0.0083 16.02)',
      3: 'oklch(95.61% 0.021 16.23)',
      4: 'oklch(92.81% 0.0473 16.68)',
      5: 'oklch(89.61% 0.062 17.33)',
      6: 'oklch(85.7% 0.0746 17.87)',
      7: 'oklch(80.72% 0.0895 18.44)',
      8: 'oklch(74.5% 0.1133 19.09)',
      9: 'oklch(62.54% 0.1935 22.98)',
      10: 'oklch(59.99% 0.1952 23.89)',
      11: 'oklch(55.72% 0.1978 25.13)',
      12: 'oklch(33.91% 0.109 16.68)',
    },
    dark: {
      1: 'oklch(18.8% 0.0133 19.39)',
      2: 'oklch(20.48% 0.0215 14.74)',
      3: 'oklch(25.04% 0.0643 13.11)',
      4: 'oklch(28.88% 0.0959 14.29)',
      5: 'oklch(33.16% 0.1066 15.53)',
      6: 'oklch(38.26% 0.1114 16.93)',
      7: 'oklch(45.06% 0.1206 18.91)',
      8: 'oklch(54.36% 0.1465 21.44)',
      9: 'oklch(62.54% 0.1935 22.98)',
      10: 'oklch(66.46% 0.1773 23.09)',
      11: 'oklch(78.37% 0.1619 22.29)',
      12: 'oklch(90.22% 0.0528 6.139)',
    },
  },
  ruby: {
    light: {
      1: 'oklch(99.38% 0.0033 356.3)',
      2: 'oklch(98.34% 0.0084 8.411)',
      3: 'oklch(95.36% 0.0222 8.563)',
      4: 'oklch(92.8% 0.0423 8.465)',
      5: 'oklch(89.64% 0.0548 8.165)',
      6: 'oklch(85.81% 0.066 7.841)',
      7: 'oklch(81.01% 0.0797 7.487)',
      8: 'oklch(74.88% 0.1014 6.861)',
      9: 'oklch(62.84% 0.195 13.19)',
      10: 'oklch(60.13% 0.1962 13.39)',
      11: 'oklch(54.89% 0.1985 13.92)',
      12: 'oklch(34.12% 0.1097 10.04)',
    },
    dark: {
      1: 'oklch(18.88% 0.0137 2.904)',
      2: 'oklch(20.77% 0.0159 5.29)',
      3: 'oklch(25.38% 0.0602 6.544)',
      4: 'oklch(29.21% 0.0888 5.505)',
      5: 'oklch(33.27% 0.0997 6.398)',
      6: 'oklch(38.2% 0.105 7.589)',
      7: 'oklch(44.83% 0.1165 9.017)',
      8: 'oklch(54.38% 0.1454 11.06)',
      9: 'oklch(62.84% 0.195 13.19)',
      10: 'oklch(66.35% 0.1794 13.69)',
      11: 'oklch(78.61% 0.1611 17.01)',
      12: 'oklch(90.54% 0.0527 355.8)',
    },
  },
  crimson: {
    light: {
      1: 'oklch(99.38% 0.0033 356.3)',
      2: 'oklch(98.18% 0.008 357.2)',
      3: 'oklch(95.41% 0.0261 356.3)',
      4: 'oklch(92.52% 0.0411 356.3)',
      5: 'oklch(89.28% 0.0537 355.8)',
      6: 'oklch(85.5% 0.0646 355.6)',
      7: 'oklch(80.78% 0.0784 354.8)',
      8: 'oklch(75% 0.0988 354.1)',
      9: 'oklch(63.43% 0.213 1.304)',
      10: 'oklch(60.8% 0.2111 2.355)',
      11: 'oklch(55.21% 0.2074 4.487)',
      12: 'oklch(34.08% 0.1129 356.8)',
    },
    dark: {
      1: 'oklch(18.93% 0.0143 354.2)',
      2: 'oklch(20.68% 0.0221 352.9)',
      3: 'oklch(25.57% 0.0595 353.9)',
      4: 'oklch(29.31% 0.0944 354.1)',
      5: 'oklch(33.26% 0.1044 354.7)',
      6: 'oklch(38.18% 0.1088 355.5)',
      7: 'oklch(45.01% 0.12 356.7)',
      8: 'oklch(54.19% 0.1482 358.3)',
      9: 'oklch(63.43% 0.213 1.304)',
      10: 'oklch(66.37% 0.1967 1.951)',
      11: 'oklch(78.74% 0.167 6.719)',
      12: 'oklch(90.88% 0.0541 346.7)',
    },
  },
  pink: {
    light: {
      1: 'oklch(99.41% 0.004 336.3)',
      2: 'oklch(98.31% 0.0094 340)',
      3: 'oklch(95.43% 0.0275 340.6)',
      4: 'oklch(92.51% 0.0416 340.7)',
      5: 'oklch(89.31% 0.0543 340.9)',
      6: 'oklch(85.54% 0.0669 340.9)',
      7: 'oklch(80.91% 0.0833 341.2)',
      8: 'oklch(75.17% 0.1067 341.6)',
      9: 'oklch(61.67% 0.2077 346.1)',
      10: 'oklch(59.57% 0.2072 346.5)',
      11: 'oklch(55.66% 0.2063 347.3)',
      12: 'oklch(35% 0.129 345.5)',
    },
    dark: {
      1: 'oklch(19.07% 0.0174 334.9)',
      2: 'oklch(20.74% 0.0313 338.2)',
      3: 'oklch(26.14% 0.0612 337.5)',
      4: 'oklch(29.94% 0.0975 340.1)',
      5: 'oklch(33.8% 0.1041 340.6)',
      6: 'oklch(38.9% 0.1072 341.6)',
      7: 'oklch(45.87% 0.1193 342.6)',
      8: 'oklch(54.54% 0.1449 344.1)',
      9: 'oklch(61.67% 0.2077 346.1)',
      10: 'oklch(65% 0.197 346.2)',
      11: 'oklch(78.87% 0.19 350.7)',
      12: 'oklch(90.53% 0.0586 343.2)',
    },
  },
  plum: {
    light: {
      1: 'oklch(99.33% 0.0047 316.9)',
      2: 'oklch(98.31% 0.0092 326)',
      3: 'oklch(95.69% 0.0274 325.5)',
      4: 'oklch(92.92% 0.0436 324.9)',
      5: 'oklch(89.85% 0.0574 324.4)',
      6: 'oklch(86.03% 0.0723 323.9)',
      7: 'oklch(80.92% 0.0915 323.1)',
      8: 'oklch(74.18% 0.12 321.9)',
      9: 'oklch(57.89% 0.1877 322.1)',
      10: 'oklch(55.27% 0.181 322.1)',
      11: 'oklch(52.13% 0.1728 322)',
      12: 'oklch(33.76% 0.1248 321.4)',
    },
    dark: {
      1: 'oklch(19% 0.0175 327)',
      2: 'oklch(21% 0.0311 327.7)',
      3: 'oklch(26.57% 0.0618 326.7)',
      4: 'oklch(30.65% 0.0883 325.3)',
      5: 'oklch(34.34% 0.0953 324.6)',
      6: 'oklch(38.89% 0.097 324.2)',
      7: 'oklch(45.65% 0.1064 323.4)',
      8: 'oklch(54.51% 0.1279 322.2)',
      9: 'oklch(57.89% 0.1877 322.1)',
      10: 'oklch(61.6% 0.1821 322.1)',
      11: 'oklch(78.59% 0.1541 322.1)',
      12: 'oklch(90.62% 0.0551 325.8)',
    },
  },
  purple: {
    light: {
      1: 'oklch(99.3% 0.0038 325.4)',
      2: 'oklch(98.23% 0.0091 313.1)',
      3: 'oklch(95.91% 0.0243 313.3)',
      4: 'oklch(93.28% 0.0387 312.1)',
      5: 'oklch(90.07% 0.0539 311.6)',
      6: 'oklch(85.96% 0.0706 311)',
      7: 'oklch(80.4% 0.0928 309.9)',
      8: 'oklch(73.31% 0.1227 308)',
      9: 'oklch(55.55% 0.183 305.9)',
      10: 'oklch(52.42% 0.1756 305.7)',
      11: 'oklch(51.6% 0.174 305.7)',
      12: 'oklch(32.23% 0.11 303.8)',
    },
    dark: {
      1: 'oklch(19.15% 0.0222 316)',
      2: 'oklch(21.31% 0.0315 314.3)',
      3: 'oklch(26.63% 0.0615 312)',
      4: 'oklch(30.82% 0.0828 310.7)',
      5: 'oklch(34.45% 0.0914 310.1)',
      6: 'oklch(38.77% 0.0967 309.7)',
      7: 'oklch(44.96% 0.108 308.7)',
      8: 'oklch(54.09% 0.1328 307.1)',
      9: 'oklch(55.55% 0.183 305.9)',
      10: 'oklch(59.59% 0.1766 306.3)',
      11: 'oklch(78.51% 0.1562 307.9)',
      12: 'oklch(91.09% 0.0486 310.9)',
    },
  },
  violet: {
    light: {
      1: 'oklch(99.21% 0.0028 308)',
      2: 'oklch(98.3% 0.009 295)',
      3: 'oklch(96.28% 0.0189 296.6)',
      4: 'oklch(93.4% 0.0392 295.4)',
      5: 'oklch(90.39% 0.0569 294.1)',
      6: 'oklch(86.41% 0.0725 293.9)',
      7: 'oklch(80.65% 0.0902 293.5)',
      8: 'oklch(72.91% 0.119 292.4)',
      9: 'oklch(54.18% 0.1789 288.1)',
      10: 'oklch(51.07% 0.177 287.6)',
      11: 'oklch(50.8% 0.1591 288.6)',
      12: 'oklch(31.27% 0.0975 286.8)',
    },
    dark: {
      1: 'oklch(19.14% 0.0261 290.5)',
      2: 'oklch(21.13% 0.0315 299.5)',
      3: 'oklch(27.09% 0.0664 293.9)',
      4: 'oklch(31.21% 0.0929 291.7)',
      5: 'oklch(34.84% 0.0986 291.4)',
      6: 'oklch(38.97% 0.1021 291.8)',
      7: 'oklch(44.46% 0.1109 291.6)',
      8: 'oklch(51.63% 0.1306 290.2)',
      9: 'oklch(54.18% 0.1789 288.1)',
      10: 'oklch(58.82% 0.1695 289.7)',
      11: 'oklch(77.82% 0.1365 293.6)',
      12: 'oklch(91.16% 0.0452 292.6)',
    },
  },
  iris: {
    light: {
      1: 'oklch(99.45% 0.0026 286.4)',
      2: 'oklch(98.12% 0.0093 284.1)',
      3: 'oklch(96.14% 0.0175 283.8)',
      4: 'oklch(93.51% 0.0353 283.4)',
      5: 'oklch(90.45% 0.0526 283.8)',
      6: 'oklch(86.46% 0.0696 283.1)',
      7: 'oklch(80.79% 0.0885 282.9)',
      8: 'oklch(72.92% 0.1183 281.5)',
      9: 'oklch(54.03% 0.1839 278.3)',
      10: 'oklch(50.86% 0.1862 277.5)',
      11: 'oklch(51.04% 0.1733 279.7)',
      12: 'oklch(31.45% 0.0989 277.4)',
    },
    dark: {
      1: 'oklch(19.28% 0.0216 284.2)',
      2: 'oklch(20.85% 0.0292 286.5)',
      3: 'oklch(27.28% 0.0686 278.6)',
      4: 'oklch(31.88% 0.102 276.1)',
      5: 'oklch(35.81% 0.1093 277.4)',
      6: 'oklch(39.88% 0.1117 279.6)',
      7: 'oklch(44.8% 0.1201 280.8)',
      8: 'oklch(50.73% 0.1381 281.1)',
      9: 'oklch(54.03% 0.1839 278.3)',
      10: 'oklch(58.67% 0.1721 281.2)',
      11: 'oklch(77.57% 0.1311 286.6)',
      12: 'oklch(91.42% 0.0419 287)',
    },
  },
  indigo: {
    light: {
      1: 'oklch(99.42% 0.0015 286.4)',
      2: 'oklch(98.29% 0.008 271.4)',
      3: 'oklch(96.07% 0.0172 268.5)',
      4: 'oklch(93.53% 0.0338 268.5)',
      5: 'oklch(90.32% 0.0511 270)',
      6: 'oklch(86.3% 0.0714 271.5)',
      7: 'oklch(80.68% 0.0869 271.4)',
      8: 'oklch(73.01% 0.1133 270.5)',
      9: 'oklch(54.36% 0.1912 267.1)',
      10: 'oklch(51.12% 0.1946 266.5)',
      11: 'oklch(50.97% 0.1728 267.1)',
      12: 'oklch(31.26% 0.0856 268.6)',
    },
    dark: {
      1: 'oklch(19.07% 0.0247 276.7)',
      2: 'oklch(20.88% 0.0302 276)',
      3: 'oklch(27.22% 0.0693 267.8)',
      4: 'oklch(31.9% 0.0932 267)',
      5: 'oklch(36.18% 0.1045 267.5)',
      6: 'oklch(40.37% 0.1106 268.3)',
      7: 'oklch(45.03% 0.1202 268.8)',
      8: 'oklch(50.27% 0.1372 268.4)',
      9: 'oklch(54.36% 0.1912 267.1)',
      10: 'oklch(58.99% 0.1757 269.2)',
      11: 'oklch(77.72% 0.1233 273.3)',
      12: 'oklch(91.1% 0.0428 270)',
    },
  },
  blue: {
    light: {
      1: 'oklch(99.32% 0.0033 247.6)',
      2: 'oklch(98.15% 0.0096 243.3)',
      3: 'oklch(96% 0.0199 236.7)',
      4: 'oklch(93.73% 0.0371 239)',
      5: 'oklch(90.6% 0.0535 242.8)',
      6: 'oklch(86.38% 0.0686 243.2)',
      7: 'oklch(80.92% 0.0884 243.3)',
      8: 'oklch(73.48% 0.1211 243)',
      9: 'oklch(64.93% 0.193 251.8)',
      10: 'oklch(62.11% 0.1837 251.9)',
      11: 'oklch(55.77% 0.1889 253)',
      12: 'oklch(32.4% 0.0965 259)',
    },
    dark: {
      1: 'oklch(19.32% 0.0258 256.8)',
      2: 'oklch(21.3% 0.0295 258.8)',
      3: 'oklch(27.41% 0.0669 254)',
      4: 'oklch(31.7% 0.1002 248.3)',
      5: 'oklch(36.49% 0.1084 249.7)',
      6: 'oklch(41.61% 0.1125 251.7)',
      7: 'oklch(47.48% 0.1211 252.8)',
      8: 'oklch(54.13% 0.1396 253.2)',
      9: 'oklch(64.93% 0.193 251.8)',
      10: 'oklch(68.9% 0.1697 251.5)',
      11: 'oklch(76.79% 0.1347 248.8)',
      12: 'oklch(90.72% 0.0511 238.3)',
    },
  },
  cyan: {
    light: {
      1: 'oklch(99.21% 0.0037 219.2)',
      2: 'oklch(98.03% 0.009 202.9)',
      3: 'oklch(95.81% 0.0265 203.6)',
      4: 'oklch(93.19% 0.0412 204.7)',
      5: 'oklch(89.98% 0.0538 206.5)',
      6: 'oklch(85.93% 0.066 207.7)',
      7: 'oklch(80.45% 0.0817 209.6)',
      8: 'oklch(72.7% 0.1099 211.7)',
      9: 'oklch(66.09% 0.1215 221.5)',
      10: 'oklch(62.67% 0.1141 221.3)',
      11: 'oklch(54.03% 0.127 223.7)',
      12: 'oklch(33.17% 0.0528 218.6)',
    },
    dark: {
      1: 'oklch(19.13% 0.0172 219.9)',
      2: 'oklch(21.41% 0.0182 225.4)',
      3: 'oklch(27.22% 0.044 222.5)',
      4: 'oklch(31.5% 0.0629 222.1)',
      5: 'oklch(36.17% 0.0703 221.5)',
      6: 'oklch(41.35% 0.0748 221.7)',
      7: 'oklch(47.77% 0.0827 221.4)',
      8: 'oklch(55.77% 0.0984 220.7)',
      9: 'oklch(66.09% 0.1215 221.5)',
      10: 'oklch(69.8% 0.1195 219.1)',
      11: 'oklch(78.56% 0.1154 213.4)',
      12: 'oklch(90.93% 0.0568 211.7)',
    },
  },
  teal: {
    light: {
      1: 'oklch(99.39% 0.0047 177.5)',
      2: 'oklch(98.12% 0.009 179)',
      3: 'oklch(96.06% 0.027 180.3)',
      4: 'oklch(93.46% 0.0418 180.2)',
      5: 'oklch(90.04% 0.0541 180.5)',
      6: 'oklch(85.58% 0.0642 181.4)',
      7: 'oklch(79.74% 0.0763 182.2)',
      8: 'oklch(72.11% 0.0972 183.5)',
      9: 'oklch(64.88% 0.1134 182)',
      10: 'oklch(61.95% 0.1096 181.2)',
      11: 'oklch(52.96% 0.1243 179)',
      12: 'oklch(32.69% 0.0504 185)',
    },
    dark: {
      1: 'oklch(18.89% 0.0123 184.5)',
      2: 'oklch(21.65% 0.0169 189.3)',
      3: 'oklch(27.37% 0.0378 186.3)',
      4: 'oklch(31.73% 0.0545 187)',
      5: 'oklch(36.25% 0.06 186.5)',
      6: 'oklch(41.37% 0.0653 185.4)',
      7: 'oklch(47.32% 0.0739 184.5)',
      8: 'oklch(53.89% 0.0866 183.5)',
      9: 'oklch(64.88% 0.1134 182)',
      10: 'oklch(68.69% 0.1227 180.3)',
      11: 'oklch(78.88% 0.1469 175.7)',
      12: 'oklch(90.51% 0.0721 175.3)',
    },
  },
  jade: {
    light: {
      1: 'oklch(99.45% 0.0037 172.6)',
      2: 'oklch(98.15% 0.0089 160.3)',
      3: 'oklch(95.96% 0.0221 161.9)',
      4: 'oklch(93.39% 0.034 163.5)',
      5: 'oklch(90.19% 0.0462 164.8)',
      6: 'oklch(85.92% 0.0592 166.9)',
      7: 'oklch(80.03% 0.0769 169.5)',
      8: 'oklch(72.09% 0.1026 173.4)',
      9: 'oklch(64.21% 0.115 170.8)',
      10: 'oklch(61.34% 0.1099 170.6)',
      11: 'oklch(52.89% 0.127 167.2)',
      12: 'oklch(32.57% 0.041 169.8)',
    },
    dark: {
      1: 'oklch(18.8% 0.014 166.3)',
      2: 'oklch(21.58% 0.018 166.1)',
      3: 'oklch(27.24% 0.0422 166.8)',
      4: 'oklch(31.64% 0.0575 168)',
      5: 'oklch(36.18% 0.0643 168.5)',
      6: 'oklch(41.08% 0.0683 169.7)',
      7: 'oklch(46.85% 0.0753 170.6)',
      8: 'oklch(53.69% 0.0879 171.8)',
      9: 'oklch(64.21% 0.115 170.8)',
      10: 'oklch(67.76% 0.1252 169.9)',
      11: 'oklch(78.54% 0.1561 167.1)',
      12: 'oklch(90.27% 0.0776 167)',
    },
  },
  green: {
    light: {
      1: 'oklch(99.43% 0.0043 159.1)',
      2: 'oklch(98.16% 0.0091 155.3)',
      3: 'oklch(95.88% 0.0229 156.4)',
      4: 'oklch(93.25% 0.0364 156.6)',
      5: 'oklch(89.95% 0.0496 157.2)',
      6: 'oklch(85.7% 0.0645 157.9)',
      7: 'oklch(79.77% 0.0839 159)',
      8: 'oklch(71.44% 0.1124 160.6)',
      9: 'oklch(64.05% 0.1327 157.8)',
      10: 'oklch(61.12% 0.1263 158.3)',
      11: 'oklch(52.9% 0.133 158.3)',
      12: 'oklch(32.18% 0.0474 164.6)',
    },
    dark: {
      1: 'oklch(18.88% 0.0127 163.3)',
      2: 'oklch(21.27% 0.016 162.2)',
      3: 'oklch(27.14% 0.0388 162.7)',
      4: 'oklch(31.82% 0.0573 161.5)',
      5: 'oklch(36.48% 0.066 161.1)',
      6: 'oklch(41.34% 0.0732 160.7)',
      7: 'oklch(46.74% 0.0828 159.9)',
      8: 'oklch(52.75% 0.0962 159.2)',
      9: 'oklch(64.05% 0.1327 157.8)',
      10: 'oklch(67.55% 0.1412 157.6)',
      11: 'oklch(77.97% 0.1657 157.2)',
      12: 'oklch(90.48% 0.0828 158.3)',
    },
  },
  grass: {
    light: {
      1: 'oklch(99.4% 0.0053 146.4)',
      2: 'oklch(98.2% 0.0092 145.9)',
      3: 'oklch(96.08% 0.0231 145.3)',
      4: 'oklch(93.42% 0.037 146.2)',
      5: 'oklch(90.13% 0.0529 146.4)',
      6: 'oklch(85.78% 0.0714 146.8)',
      7: 'oklch(79.85% 0.095 147.5)',
      8: 'oklch(71.65% 0.13 148.3)',
      9: 'oklch(65.16% 0.1469 147.4)',
      10: 'oklch(61.38% 0.1416 147.3)',
      11: 'oklch(52.68% 0.1296 147.2)',
      12: 'oklch(32.68% 0.0535 148.6)',
    },
    dark: {
      1: 'oklch(18.83% 0.0141 155.8)',
      2: 'oklch(21.1% 0.0141 151.5)',
      3: 'oklch(26.71% 0.03 150.7)',
      4: 'oklch(31.91% 0.0522 150.4)',
      5: 'oklch(36.68% 0.0619 149.9)',
      6: 'oklch(41.64% 0.0721 149.3)',
      7: 'oklch(46.83% 0.0835 148.9)',
      8: 'oklch(52.38% 0.0973 148.2)',
      9: 'oklch(65.16% 0.1469 147.4)',
      10: 'oklch(68.98% 0.1454 147.6)',
      11: 'oklch(77.98% 0.142 148.5)',
      12: 'oklch(91.1% 0.078 144.9)',
    },
  },
  bronze: {
    light: {
      1: 'oklch(99.15% 0.001 15.92)',
      2: 'oklch(98.06% 0.0073 42.99)',
      3: 'oklch(95.28% 0.0102 44.26)',
      4: 'oklch(92.52% 0.0138 43.57)',
      5: 'oklch(89.54% 0.0178 42.64)',
      6: 'oklch(85.99% 0.0225 43.4)',
      7: 'oklch(81.13% 0.0295 41.94)',
      8: 'oklch(74.18% 0.0388 41.18)',
      9: 'oklch(62.75% 0.046 44.29)',
      10: 'oklch(58.8% 0.0452 42.44)',
      11: 'oklch(51.05% 0.0437 38.37)',
      12: 'oklch(32.92% 0.0293 35.24)',
    },
    dark: {
      1: 'oklch(18.06% 0.0053 41.55)',
      2: 'oklch(21.47% 0.005 41.54)',
      3: 'oklch(25.45% 0.0081 42.65)',
      4: 'oklch(29.11% 0.0102 45.5)',
      5: 'oklch(32.94% 0.0128 43.31)',
      6: 'oklch(37.44% 0.016 44.26)',
      7: 'oklch(42.99% 0.02 44.84)',
      8: 'oklch(49.86% 0.0245 45.17)',
      9: 'oklch(62.75% 0.046 44.29)',
      10: 'oklch(66.87% 0.0453 44.29)',
      11: 'oklch(79.18% 0.043 44.5)',
      12: 'oklch(91.51% 0.0174 50.3)',
    },
  },
  gold: {
    light: {
      1: 'oklch(99.37% 0.0011 106.4)',
      2: 'oklch(98.09% 0.0086 97.34)',
      3: 'oklch(95.34% 0.0118 94.69)',
      4: 'oklch(92.57% 0.015 93.18)',
      5: 'oklch(89.58% 0.0192 90.73)',
      6: 'oklch(85.96% 0.0247 88.38)',
      7: 'oklch(81.01% 0.0317 83.41)',
      8: 'oklch(73.86% 0.0422 78.17)',
      9: 'oklch(62.06% 0.0493 78.13)',
      10: 'oklch(58.77% 0.0465 77.42)',
      11: 'oklch(50.39% 0.0393 78.1)',
      12: 'oklch(33.21% 0.0189 81.67)',
    },
    dark: {
      1: 'oklch(18.22% 0.0023 106.6)',
      2: 'oklch(21.65% 0.0061 92.49)',
      3: 'oklch(25.49% 0.0076 88.28)',
      4: 'oklch(29.08% 0.0095 86.76)',
      5: 'oklch(32.89% 0.0115 83.66)',
      6: 'oklch(37.36% 0.0138 82.43)',
      7: 'oklch(42.94% 0.0165 80.92)',
      8: 'oklch(49.83% 0.0204 79.5)',
      9: 'oklch(62.06% 0.0493 78.13)',
      10: 'oklch(66.22% 0.0473 77.18)',
      11: 'oklch(79.4% 0.041 77.18)',
      12: 'oklch(91.51% 0.0137 77.44)',
    },
  },
  brown: {
    light: {
      1: 'oklch(99.43% 0.0015 63.24)',
      2: 'oklch(98.32% 0.0058 65.73)',
      3: 'oklch(95.41% 0.0128 67.82)',
      4: 'oklch(92.61% 0.0203 66.85)',
      5: 'oklch(89.69% 0.0286 65.79)',
      6: 'oklch(86.24% 0.039 65.47)',
      7: 'oklch(81.53% 0.0531 64.22)',
      8: 'oklch(74.67% 0.0717 62.45)',
      9: 'oklch(63.27% 0.0784 60.77)',
      10: 'oklch(59.67% 0.0725 59.28)',
      11: 'oklch(51.21% 0.0583 55.46)',
      12: 'oklch(33.15% 0.018 46.98)',
    },
    dark: {
      1: 'oklch(17.85% 0.005 81.55)',
      2: 'oklch(21.31% 0.0075 51.62)',
      3: 'oklch(25.36% 0.0128 53.48)',
      4: 'oklch(28.84% 0.0182 55.42)',
      5: 'oklch(32.47% 0.0243 55.88)',
      6: 'oklch(37.02% 0.0317 57.69)',
      7: 'oklch(42.9% 0.0409 59.83)',
      8: 'oklch(50.86% 0.0535 62.17)',
      9: 'oklch(63.27% 0.0784 60.77)',
      10: 'oklch(67.44% 0.0749 61.48)',
      11: 'oklch(79.82% 0.0628 62.51)',
      12: 'oklch(91.76% 0.036 75.67)',
    },
  },
  orange: {
    light: {
      1: 'oklch(99.22% 0.0026 40.73)',
      2: 'oklch(97.87% 0.0154 70.83)',
      3: 'oklch(95.76% 0.0368 78.11)',
      4: 'oklch(92.03% 0.0811 74.16)',
      5: 'oklch(89.04% 0.1066 70.84)',
      6: 'oklch(85.73% 0.11 64.43)',
      7: 'oklch(80.53% 0.1126 59.88)',
      8: 'oklch(74.54% 0.1321 54.82)',
      9: 'oklch(68.9% 0.1911 44.8)',
      10: 'oklch(66.22% 0.1944 43.49)',
      11: 'oklch(59.19% 0.186 46.95)',
      12: 'oklch(35.01% 0.0687 40.82)',
    },
    dark: {
      1: 'oklch(18.64% 0.0119 53.75)',
      2: 'oklch(20.88% 0.0192 67.38)',
      3: 'oklch(25.75% 0.0452 59.54)',
      4: 'oklch(29.33% 0.0796 58.65)',
      5: 'oklch(33.46% 0.0868 58.09)',
      6: 'oklch(38.63% 0.0869 55.15)',
      7: 'oklch(45.34% 0.0947 53.06)',
      8: 'oklch(54.05% 0.1162 49.75)',
      9: 'oklch(68.9% 0.1911 44.8)',
      10: 'oklch(74.14% 0.1991 46.32)',
      11: 'oklch(79.9% 0.1631 50.77)',
      12: 'oklch(92.48% 0.0524 66.22)',
    },
  },
  amber: {
    light: {
      1: 'oklch(99.4% 0.0028 84.62)',
      2: 'oklch(98.62% 0.024 100.3)',
      3: 'oklch(96.9% 0.0686 100.4)',
      4: 'oklch(94.54% 0.1043 98.19)',
      5: 'oklch(91.84% 0.1333 98.4)',
      6: 'oklch(88.12% 0.1237 93.24)',
      7: 'oklch(82.74% 0.122 86.18)',
      8: 'oklch(75.82% 0.1404 76.7)',
      9: 'oklch(85.62% 0.1778 81.37)',
      10: 'oklch(83.15% 0.1679 80.94)',
      11: 'oklch(56.92% 0.1439 67.28)',
      12: 'oklch(35.19% 0.0485 53.74)',
    },
    dark: {
      1: 'oklch(18.36% 0.0126 77.27)',
      2: 'oklch(21.17% 0.0179 78.68)',
      3: 'oklch(25.75% 0.0437 74.56)',
      4: 'oklch(29.62% 0.0715 76.25)',
      5: 'oklch(33.69% 0.0812 76.41)',
      6: 'oklch(38.71% 0.0784 75.09)',
      7: 'oklch(45.32% 0.0817 74.55)',
      8: 'oklch(53.6% 0.0964 73.57)',
      9: 'oklch(85.62% 0.1778 81.37)',
      10: 'oklch(90.15% 0.2065 96.98)',
      11: 'oklch(87.06% 0.1754 85.07)',
      12: 'oklch(93.49% 0.0715 85.99)',
    },
  },
  yellow: {
    light: {
      1: 'oklch(99.28% 0.0052 106.5)',
      2: 'oklch(98.84% 0.025 102.9)',
      3: 'oklch(97.4% 0.0845 104.4)',
      4: 'oklch(95.27% 0.1166 101.7)',
      5: 'oklch(92.51% 0.1409 98.2)',
      6: 'oklch(88.09% 0.1339 95.42)',
      7: 'oklch(83.55% 0.1203 92.79)',
      8: 'oklch(76.61% 0.137 89.85)',
      9: 'oklch(92.66% 0.2086 102.1)',
      10: 'oklch(89.67% 0.1851 97.53)',
      11: 'oklch(57.44% 0.1364 81.69)',
      12: 'oklch(35.78% 0.046 86.63)',
    },
    dark: {
      1: 'oklch(18.11% 0.013 86.01)',
      2: 'oklch(20.88% 0.017 92.09)',
      3: 'oklch(25.9% 0.0466 90.29)',
      4: 'oklch(29.26% 0.0694 94.2)',
      5: 'oklch(33.34% 0.0791 94.59)',
      6: 'oklch(38.43% 0.0774 93.3)',
      7: 'oklch(45.16% 0.0809 91.5)',
      8: 'oklch(53.49% 0.0951 89.93)',
      9: 'oklch(92.66% 0.2086 102.1)',
      10: 'oklch(97.11% 0.1822 109.4)',
      11: 'oklch(90.02% 0.1664 101.6)',
      12: 'oklch(94.13% 0.0747 101)',
    },
  },
  lime: {
    light: {
      1: 'oklch(99.24% 0.0042 120.7)',
      2: 'oklch(98.17% 0.0098 116.7)',
      3: 'oklch(95.94% 0.0433 119.1)',
      4: 'oklch(93.21% 0.0689 120.4)',
      5: 'oklch(89.74% 0.087 121.8)',
      6: 'oklch(85.3% 0.0993 123.4)',
      7: 'oklch(79.46% 0.1107 125.3)',
      8: 'oklch(72.36% 0.1348 128.1)',
      9: 'oklch(88.75% 0.1749 126.1)',
      10: 'oklch(85.89% 0.1877 126.7)',
      11: 'oklch(54.38% 0.1113 128.6)',
      12: 'oklch(35.37% 0.0574 121.2)',
    },
    dark: {
      1: 'oklch(18.04% 0.0144 120.3)',
      2: 'oklch(20.8% 0.0194 129)',
      3: 'oklch(26.62% 0.0343 132.2)',
      4: 'oklch(31.65% 0.0469 131.8)',
      5: 'oklch(36.34% 0.0579 131.6)',
      6: 'oklch(41.16% 0.0686 131.4)',
      7: 'oklch(46.44% 0.08 131.2)',
      8: 'oklch(52.43% 0.0945 130.9)',
      9: 'oklch(88.75% 0.1749 126.1)',
      10: 'oklch(94.18% 0.1754 123.7)',
      11: 'oklch(86.78% 0.1557 124.8)',
      12: 'oklch(94.64% 0.0819 122.6)',
    },
  },
  mint: {
    light: {
      1: 'oklch(99.28% 0.0053 183.7)',
      2: 'oklch(98.2% 0.0101 178.7)',
      3: 'oklch(95.95% 0.0299 179.6)',
      4: 'oklch(93.3% 0.047 179)',
      5: 'oklch(89.94% 0.0608 178.7)',
      6: 'oklch(85.59% 0.0719 178.7)',
      7: 'oklch(79.79% 0.0848 178.2)',
      8: 'oklch(72.2% 0.1066 177.7)',
      9: 'oklch(86.96% 0.0998 178)',
      10: 'oklch(84.12% 0.0997 177.8)',
      11: 'oklch(51.18% 0.095 176)',
      12: 'oklch(34.97% 0.0505 181.4)',
    },
    dark: {
      1: 'oklch(18.84% 0.0116 192.3)',
      2: 'oklch(21.01% 0.0172 196.6)',
      3: 'oklch(26.87% 0.039 192.5)',
      4: 'oklch(31.35% 0.058 191.6)',
      5: 'oklch(35.94% 0.0634 188.9)',
      6: 'oklch(41.04% 0.0665 186.3)',
      7: 'oklch(47.09% 0.0728 183.4)',
      8: 'oklch(54.01% 0.085 179.6)',
      9: 'oklch(86.96% 0.0998 178)',
      10: 'oklch(91.62% 0.0796 179.4)',
      11: 'oklch(79.55% 0.1184 176.4)',
      12: 'oklch(93.05% 0.0569 168.5)',
    },
  },
  sky: {
    light: {
      1: 'oklch(99.33% 0.0053 211.9)',
      2: 'oklch(97.99% 0.0099 217.7)',
      3: 'oklch(96.06% 0.0238 219.6)',
      4: 'oklch(93.56% 0.0356 220.4)',
      5: 'oklch(90.28% 0.0465 221.4)',
      6: 'oklch(86.1% 0.0577 222.9)',
      7: 'oklch(80.58% 0.0717 225.7)',
      8: 'oklch(72.94% 0.0962 228)',
      9: 'oklch(86.14% 0.1027 217.8)',
      10: 'oklch(83.77% 0.1037 219.6)',
      11: 'oklch(52.56% 0.108 232.5)',
      12: 'oklch(35.11% 0.0572 241.6)',
    },
    dark: {
      1: 'oklch(18.98% 0.024 257.1)',
      2: 'oklch(21.61% 0.0289 258.5)',
      3: 'oklch(27.11% 0.0539 251.9)',
      4: 'oklch(32.22% 0.0696 247.8)',
      5: 'oklch(37.25% 0.0791 245.8)',
      6: 'oklch(42.69% 0.0874 243.6)',
      7: 'oklch(48.8% 0.0981 240.7)',
      8: 'oklch(55.74% 0.1143 237.2)',
      9: 'oklch(86.14% 0.1027 217.8)',
      10: 'oklch(90.81% 0.073 214.9)',
      11: 'oklch(79.26% 0.0986 231.7)',
      12: 'oklch(93.37% 0.0526 214.4)',
    },
  },
}
