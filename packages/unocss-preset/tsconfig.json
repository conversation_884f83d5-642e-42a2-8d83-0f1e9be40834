{"compilerOptions": {"target": "esnext", "lib": ["esnext"], "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "strict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "sourceMap": true, "esModuleInterop": true, "skipLibCheck": true}, "exclude": ["**/dist/**", "**/node_modules/**", "**/test/**", "**/playground/**", "tsdown.config.ts", "uno.config.ts"]}