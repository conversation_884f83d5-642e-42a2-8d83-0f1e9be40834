{"name": "@weme-ui/unocss-preset", "type": "module", "version": "0.0.0", "packageManager": "pnpm@10.18.1", "description": "Weme UI - UnoCSS Preset", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/weme-ui/weme-ui#readme", "repository": {"type": "git", "url": "git+https://github.com/weme-ui/weme-ui.git", "directory": "packages/unocss-preset"}, "bugs": {"url": "https://github.com/weme-ui/weme-ui/issues"}, "keywords": ["unocss", "unocss-preset", "weme-ui"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "scripts": {"build": "esno scripts/prepare.ts && tsdown", "dev": "tsdown --watch", "dev:prepare": "pnpm run build", "prepublishOnly": "pnpm run build", "test": "vitest", "typecheck": "tsgo --noEmit", "play": "vite playground"}, "peerDependencies": {"@radix-ui/colors": "catalog:dev", "@unocss/rule-utils": "catalog:dev", "@weme-ui/colors": "workspace:*", "@weme-ui/schema": "catalog:dev", "bezier-easing": "catalog:dev", "colorjs.io": "catalog:dev", "defu": "catalog:dev", "unocss": "catalog:dev"}, "devDependencies": {"@radix-ui/colors": "catalog:dev", "@types/node": "catalog:build", "@unocss/rule-utils": "catalog:dev", "@vitejs/plugin-vue": "catalog:vue", "@vueuse/core": "catalog:dev", "@weme-ui/colors": "workspace:*", "@weme-ui/schema": "catalog:dev", "bezier-easing": "catalog:dev", "bumpp": "catalog:build", "colorjs.io": "catalog:dev", "defu": "catalog:dev", "esno": "catalog:utils", "reka-ui": "catalog:dev", "tsdown": "catalog:build", "typescript": "catalog:build", "unocss": "catalog:dev", "vite": "catalog:build", "vite-plugin-vue-devtools": "catalog:dev", "vitest": "catalog:testing", "vue": "catalog:vue", "vue-router": "catalog:vue"}}