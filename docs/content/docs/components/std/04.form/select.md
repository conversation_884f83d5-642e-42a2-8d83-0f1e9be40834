---
description: 显示供用户选择的选项列表 - 由按钮触发。

links:
  - label: Form
    icon: lucide:component
  - label: Source
    icon: lucide:code
    to: https://github.com/weme-ui/weme-ui/tree/main/registry/std/components/select
    target: _blank
---

::preview
#preview
:ExamplesStdSelectOverview
::

## 安装

```shell [Terminal]
$ pnpm dlx @weme-ui/weme-ui add select
```

## API

### 属性

::props-table
---

---
::

### 插槽

::slots-table
---

---
::

### 事件

::emits-table
---

---
::

### 样式

::styles-table
---

---
::

## 示例

