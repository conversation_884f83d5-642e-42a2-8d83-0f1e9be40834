{"$schema": "https://weme-ui.github.io/schema/registry.json", "name": "weme-ui/std", "description": "The standard components for Weme UI.", "version": "0.0.0", "prefix": "ui", "directory": "registry/std", "access": "public", "meta": {"authors": ["<PERSON> <<EMAIL>>"], "homepage": "https://github.com/weme-ui/weme-ui", "repository": "https://github.com/weme-ui/weme-ui", "bugs": "https://github.com/weme-ui/weme-ui/issues"}, "dependencies": ["clsx", "tailwind-merge", "tailwind-variants"], "devDependencies": ["@nuxt/icon", "@unocss/nuxt", "@vueuse/core", "@weme-ui/nuxt", "@weme-ui/unocss-preset", "reka-ui", "unocss", "vue"], "items": [{"name": "icon", "type": "component", "title": "Icon", "description": "A component to display any icon from Iconify.", "categories": ["general"], "files": [{"type": "component", "path": "components/icon/icon.vue"}, {"type": "type", "path": "components/icon/icon.props.ts"}, {"type": "component", "path": "components/icon/icon-box.vue"}, {"type": "type", "path": "components/icon/icon-box.props.ts"}, {"type": "style", "path": "components/icon/icon-box.style.ts"}]}, {"name": "typography", "type": "component", "title": "Typography", "description": "Styles for headings, paragraphs, text... etc.", "categories": ["general"], "files": [{"type": "component", "path": "components/typography/typography.vue"}, {"type": "type", "path": "components/typography/typography.props.ts"}, {"type": "style", "path": "components/typography/typography.style.ts"}]}, {"name": "button", "type": "component", "title": "<PERSON><PERSON>", "description": "Displays a button or a component that looks like a button.", "categories": ["general"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/button/button.vue"}, {"type": "type", "path": "components/button/button.props.ts"}, {"type": "style", "path": "components/button/button.style.ts"}]}, {"name": "link", "type": "component", "title": "Link", "description": "A wrapper around with extra <NuxtLink /> props.", "categories": ["general"], "files": [{"type": "component", "path": "components/link/link.vue"}, {"type": "type", "path": "components/link/link.props.ts"}, {"type": "style", "path": "components/link/link.style.ts"}]}, {"name": "kbd", "type": "component", "title": "Kbd", "description": "A kbd element to display a keyboard key.", "categories": ["data-display"], "files": [{"type": "component", "path": "components/kbd/kbd.vue"}, {"type": "type", "path": "components/kbd/kbd.props.ts"}, {"type": "style", "path": "components/kbd/kbd.style.ts"}, {"type": "composable", "path": "composables/use-kbd.ts"}]}, {"name": "badge", "type": "component", "title": "Badge", "description": "Displays a badge or a component that looks like a badge.", "categories": ["data-display"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/badge/badge.vue"}, {"type": "type", "path": "components/badge/badge.props.ts"}, {"type": "style", "path": "components/badge/badge.style.ts"}]}, {"name": "alert", "type": "component", "title": "<PERSON><PERSON>", "description": "Displays a callout for user attention.", "categories": ["data-display"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/alert/alert.vue"}, {"type": "type", "path": "components/alert/alert.props.ts"}, {"type": "style", "path": "components/alert/alert.style.ts"}]}, {"name": "separator", "type": "component", "title": "Separator", "description": "Visually or semantically separates content.", "categories": ["layout"], "files": [{"type": "component", "path": "components/separator/separator.vue"}, {"type": "type", "path": "components/separator/separator.props.ts"}, {"type": "style", "path": "components/separator/separator.style.ts"}]}, {"name": "space", "type": "component", "title": "Space", "description": "Set the spacing and separators between components.", "categories": ["layout"], "files": [{"type": "component", "path": "components/space/space.vue"}, {"type": "type", "path": "components/space/space.props.ts"}, {"type": "style", "path": "components/space/space.style.ts"}]}, {"name": "card", "type": "component", "title": "Card", "description": "Displays a card with header, content, and footer.", "categories": ["data-display"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/card/card.vue"}, {"type": "type", "path": "components/card/card.props.ts"}, {"type": "style", "path": "components/card/card.style.ts"}], "cssVars": {"card": {"bg": "color.neutral.1", "fg": "color.neutral.11"}}}, {"name": "avatar", "type": "component", "title": "Avatar", "description": "An image element with a fallback for representing the user.", "categories": ["data-display"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/avatar/avatar.vue"}, {"type": "type", "path": "components/avatar/avatar.props.ts"}, {"type": "style", "path": "components/avatar/avatar.style.ts"}, {"type": "component", "path": "components/avatar/avatar-group.vue"}, {"type": "type", "path": "components/avatar/avatar-group.props.ts"}, {"type": "style", "path": "components/avatar/avatar-group.style.ts"}]}, {"name": "scroll-area", "type": "component", "title": "Scroll Area", "description": "Augments native scroll functionality for custom, cross-browser styling.", "categories": ["other"], "files": [{"type": "component", "path": "components/scroll-area/scroll-area.vue"}, {"type": "type", "path": "components/scroll-area/scroll-area.props.ts"}, {"type": "style", "path": "components/scroll-area/scroll-area.style.ts"}]}, {"name": "breadcrumb", "type": "component", "title": "Breadcrumb", "description": "Displays the path to the current resource using a hierarchy of links.", "categories": ["navigation"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/breadcrumb/breadcrumb.vue"}, {"type": "type", "path": "components/breadcrumb/breadcrumb.props.ts"}, {"type": "style", "path": "components/breadcrumb/breadcrumb.style.ts"}]}, {"name": "skeleton", "type": "component", "title": "Skeleton", "description": "Use to show a placeholder while content is loading.", "categories": ["feedback"], "files": [{"type": "component", "path": "components/skeleton/skeleton.vue"}, {"type": "type", "path": "components/skeleton/skeleton.props.ts"}, {"type": "style", "path": "components/skeleton/skeleton.style.ts"}]}, {"name": "chip", "type": "component", "title": "Chip", "description": "An indicator of a numeric value or a state.", "categories": ["data-display"], "files": [{"type": "component", "path": "components/chip/chip.vue"}, {"type": "type", "path": "components/chip/chip.props.ts"}, {"type": "style", "path": "components/chip/chip.style.ts"}]}, {"name": "collapsible", "type": "component", "title": "Collapsible", "description": "An interactive component which expands/collapses a panel.", "categories": ["data-display"], "files": [{"type": "component", "path": "components/collapsible/collapsible.vue"}, {"type": "type", "path": "components/collapsible/collapsible.props.ts"}, {"type": "style", "path": "components/collapsible/collapsible.style.ts"}]}, {"name": "tooltip", "type": "component", "title": "<PERSON><PERSON><PERSON>", "description": "A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it.", "categories": ["data-display"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/tooltip/tooltip.vue"}, {"type": "type", "path": "components/tooltip/tooltip.props.ts"}, {"type": "style", "path": "components/tooltip/tooltip.style.ts"}], "cssVars": {"tooltip": {"bg": "color.neutral.11", "fg": "color.neutral.1"}}}, {"name": "popover", "type": "component", "title": "Popover", "description": "Displays rich content in a portal, triggered by a button.", "categories": ["feedback"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/popover/popover.vue"}, {"type": "type", "path": "components/popover/popover.props.ts"}, {"type": "style", "path": "components/popover/popover.style.ts"}], "cssVars": {"popover": {"bg": "color.neutral.1"}}}, {"name": "hover-card", "type": "component", "title": "Hover Card", "description": "For sighted users to preview content available behind a link.", "categories": ["feedback"], "files": [{"type": "component", "path": "components/hover-card/hover-card.vue"}, {"type": "type", "path": "components/hover-card/hover-card.props.ts"}, {"type": "style", "path": "components/hover-card/hover-card.style.ts"}], "cssVars": {"popover": {"bg": "color.neutral.1"}}}, {"name": "pop-confirm", "type": "component", "title": "Pop Confirm", "description": "Click on the button element to pop up a bubble-like confirmation box.", "categories": ["feedback"], "registryDependencies": ["button", "icon", "popover"], "files": [{"type": "component", "path": "components/pop-confirm/pop-confirm.vue"}, {"type": "type", "path": "components/pop-confirm/pop-confirm.props.ts"}, {"type": "style", "path": "components/pop-confirm/pop-confirm.style.ts"}]}, {"name": "modal", "type": "component", "title": "Modal", "description": "A window overlaid on either the primary window or another dialog window, rendering the content underneath inert.", "categories": ["feedback"], "registryDependencies": ["icon", "scroll-area"], "files": [{"type": "component", "path": "components/modal/modal.vue"}, {"type": "type", "path": "components/modal/modal.props.ts"}, {"type": "style", "path": "components/modal/modal.style.ts"}], "cssVars": {"modal": {"bg": "color.neutral.1", "width": "24rem", "height": "12rem"}}}, {"name": "empty", "type": "component", "title": "Empty", "description": "A default state that is rendered when there is no corresponding data content.", "categories": ["data-display"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/empty/empty.vue"}, {"type": "type", "path": "components/empty/empty.props.ts"}, {"type": "style", "path": "components/empty/empty.style.ts"}]}, {"name": "result", "type": "component", "title": "Result", "description": "Displays the result of an operation, which can display a title, description, image, and operation button.", "categories": ["feedback"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/result/result.vue"}, {"type": "type", "path": "components/result/result.props.ts"}, {"type": "style", "path": "components/result/result.style.ts"}]}, {"name": "toast", "type": "component", "title": "Toast", "description": "A succinct message that is displayed temporarily.", "categories": ["feedback"], "registryDependencies": ["button", "icon"], "files": [{"type": "component", "path": "components/toast/toast.vue"}, {"type": "type", "path": "components/toast/toast.props.ts"}, {"type": "style", "path": "components/toast/toast.style.ts"}, {"type": "component", "path": "components/toast/toast-provider.vue"}, {"type": "type", "path": "components/toast/toast-provider.props.ts"}, {"type": "style", "path": "components/toast/toast-provider.style.ts"}, {"type": "composable", "path": "composables/use-toast.ts"}], "cssVars": {"toast": {"bg": "color.neutral.1", "ring": "color.neutral.1", "width": "24rem"}}}, {"name": "accordion", "type": "component", "title": "Accordion", "description": "A vertically stacked set of interactive headings that each reveal an associated section of content.", "categories": ["data-display"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/accordion/accordion.vue"}, {"type": "type", "path": "components/accordion/accordion.props.ts"}, {"type": "style", "path": "components/accordion/accordion.style.ts"}], "cssVars": {"accordion": {"bg": "color.neutral.1"}}}, {"name": "tabs", "type": "component", "title": "Tabs", "description": "A set of layered sections of content—known as tab panels—that are displayed one at a time.", "categories": ["data-display"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/tabs/tabs.vue"}, {"type": "type", "path": "components/tabs/tabs.props.ts"}, {"type": "style", "path": "components/tabs/tabs.style.ts"}, {"type": "component", "path": "components/tabs/tabs-content.vue"}, {"type": "type", "path": "components/tabs/tabs-content.props.ts"}, {"type": "style", "path": "components/tabs/tabs-content.style.ts"}], "cssVars": {"tabs": {"bg": "color.neutral.1"}}}, {"name": "dropdown", "type": "component", "title": "Dropdown", "description": "Displays a menu to the user — such as a set of actions or functions — triggered by a button.", "categories": ["navigation"], "registryDependencies": ["icon", "kbd"], "files": [{"type": "component", "path": "components/dropdown/dropdown.vue"}, {"type": "type", "path": "components/dropdown/dropdown.props.ts"}, {"type": "style", "path": "components/dropdown/dropdown.style.ts"}, {"type": "component", "path": "components/dropdown/dropdown-content.vue"}, {"type": "type", "path": "components/dropdown/dropdown-content.props.ts"}, {"type": "style", "path": "components/dropdown/dropdown-content.style.ts"}], "cssVars": {"dropdown": {"bg": "color.neutral.1"}}}, {"name": "overflow-list", "type": "component", "title": "Overflow List", "description": "A container component that hides sub-components that exceed the number limit.", "categories": ["other"], "files": [{"type": "component", "path": "components/overflow-list/overflow-list.vue"}, {"type": "type", "path": "components/overflow-list/overflow-list.props.ts"}, {"type": "style", "path": "components/overflow-list/overflow-list.style.ts"}]}, {"name": "spinner", "type": "component", "title": "Spinner", "description": "Displays an animated loading indicator.", "categories": ["feedback"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/spinner/spinner.vue"}, {"type": "type", "path": "components/spinner/spinner.props.ts"}, {"type": "style", "path": "components/spinner/spinner.style.ts"}]}, {"name": "pagination", "type": "component", "title": "Pagination", "description": "Displays data in paged format and provides navigation between pages.", "categories": ["navigation"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/pagination/pagination.vue"}, {"type": "type", "path": "components/pagination/pagination.props.ts"}, {"type": "style", "path": "components/pagination/pagination.style.ts"}]}, {"name": "progress", "type": "component", "title": "Progress", "description": "Displays an indicator showing the completion progress of a task, typically displayed as a progress bar.", "categories": ["feedback"], "files": [{"type": "component", "path": "components/progress/progress.vue"}, {"type": "type", "path": "components/progress/progress.props.ts"}, {"type": "style", "path": "components/progress/progress.style.ts"}]}, {"name": "timeline", "type": "component", "title": "Timeline", "description": "A component that displays a sequence of events with dates, titles, icons.", "categories": ["data-display"], "registryDependencies": ["icon", "separator"], "files": [{"type": "component", "path": "components/timeline/timeline.vue"}, {"type": "type", "path": "components/timeline/timeline.props.ts"}, {"type": "style", "path": "components/timeline/timeline.style.ts"}]}, {"name": "stepper", "type": "component", "title": "Stepper", "description": "A set of steps that are used to indicate progress through a multi-step process.", "categories": ["navigation"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/stepper/stepper.vue"}, {"type": "type", "path": "components/stepper/stepper.props.ts"}, {"type": "style", "path": "components/stepper/stepper.style.ts"}]}, {"name": "switch", "type": "component", "title": "Switch", "description": "A control that allows the user to toggle between checked and not checked.", "categories": ["form"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/switch/switch.vue"}, {"type": "type", "path": "components/switch/switch.props.ts"}, {"type": "style", "path": "components/switch/switch.style.ts"}]}, {"name": "toggle", "type": "component", "title": "Toggle", "description": "A two-state button that can be either on or off.", "categories": ["form"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/toggle/toggle.vue"}, {"type": "type", "path": "components/toggle/toggle.props.ts"}, {"type": "style", "path": "components/toggle/toggle.style.ts"}, {"type": "component", "path": "components/toggle/toggle-group.vue"}, {"type": "type", "path": "components/toggle/toggle-group.props.ts"}, {"type": "style", "path": "components/toggle/toggle-group.style.ts"}]}, {"name": "data-list", "type": "component", "title": "Data List", "description": "Displays metadata as a list of key-value pairs.", "categories": ["data-display"], "files": [{"type": "component", "path": "components/data-list/data-list.vue"}, {"type": "type", "path": "components/data-list/data-list.props.ts"}, {"type": "style", "path": "components/data-list/data-list.style.ts"}]}, {"name": "label", "type": "component", "title": "Label", "description": "Renders an accessible label associated with controls.", "categories": ["form"], "files": [{"type": "component", "path": "components/label/label.vue"}, {"type": "type", "path": "components/label/label.props.ts"}, {"type": "style", "path": "components/label/label.style.ts"}]}, {"name": "input", "type": "component", "title": "Input", "description": "Displays a form input field or a component that looks like an input field.", "categories": ["form"], "registryDependencies": ["icon"], "files": [{"type": "component", "path": "components/input/input.vue"}, {"type": "type", "path": "components/input/input.props.ts"}, {"type": "style", "path": "components/input/input.style.ts"}, {"type": "component", "path": "components/input/input-password.vue"}, {"type": "type", "path": "components/input/input-password.props.ts"}, {"type": "style", "path": "components/input/input-password.style.ts"}, {"type": "component", "path": "components/input/input-pin.vue"}, {"type": "type", "path": "components/input/input-pin.props.ts"}, {"type": "style", "path": "components/input/input-pin.style.ts"}, {"type": "component", "path": "components/input/input-number.vue"}, {"type": "type", "path": "components/input/input-number.props.ts"}, {"type": "style", "path": "components/input/input-number.style.ts"}]}, {"name": "select", "type": "component", "title": "Select", "description": "Displays a list of options for the user to pick from - triggered by a button.", "categories": ["form"], "registryDependencies": ["avatar", "chip", "empty", "icon"], "files": [{"type": "component", "path": "components/select/select.vue"}, {"type": "type", "path": "components/select/select.props.ts"}, {"type": "style", "path": "components/select/select.style.ts"}]}]}