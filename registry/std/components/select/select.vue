<script lang="ts" setup>
import type { SelectContentProps } from 'reka-ui'
import type { SelectEmits, SelectItem, SelectProps } from './select.props'
import { reactivePick } from '@vueuse/core'
import { defu } from 'defu'
import { useForwardPropsEmits } from 'reka-ui'
import { Select } from 'reka-ui/namespaced'
import { computed, toRef } from 'vue'
import { cn } from '~/utils/styles'
import Icon from '../icon/icon.vue'
import { useSelectStyle } from './select.style'

const props = withDefaults(defineProps<SelectProps>(), {
  placeholder: '',
  trailingIcon: 'select',
  indicatorIcon: 'check',
  color: 'accent',
  variant: 'normal',
  radius: 'sm',
  size: 'md',
})

const emits = defineEmits<SelectEmits>()

const rootProps = useForwardPropsEmits(
  reactivePick(props, 'autocomplete', 'by', 'defaultOpen', 'defaultValue', 'dir', 'disabled', 'modelValue', 'multiple', 'name', 'open', 'required'),
  emits,
)

const contentProps = toRef(() => defu(props.contentProps, {
  side: 'bottom',
  align: 'start',
  position: 'popper',
  collisionPadding: 8,
  sideOffset: 8,
} as SelectContentProps))

const ui = computed(() => useSelectStyle(props))

const grouped = computed<SelectItem[][]>(() => {
  if (!props.items?.length) {
    return []
  }

  return (Array.isArray(props.items) && Array.isArray(props.items[0]))
    ? props.items as SelectItem[][]
    : [props.items]
})

const items = computed(() => grouped.value.flatMap(g => g) as SelectItem[])
</script>

<template>
  <Select.Root v-slot="{ open, modelValue: value }" v-bind="rootProps">
    <Select.Trigger v-bind="$attrs" :id="id" :class="ui.trigger({ class: cn(props.class, props.ui?.trigger) })">
      <span v-if="leadingIcon || $slots.leading" :class="ui.leading({ class: props.ui?.leading })">
        <slot name="leading" v-bind="{ open, value }">
          <Icon v-if="leadingIcon" :name="leadingIcon" :class="ui.leadingIcon({ class: props.ui?.leadingIcon })" />
        </slot>
      </span>

      <Select.Value :placeholder="placeholder">
        <slot name="value" v-bind="{ open, value }">
          {{ value }}
        </slot>
      </Select.Value>

      <span :class="ui.trailing({ class: props.ui?.trailing })">
        <slot name="trailing" v-bind="{ open, value }">
          <Icon :name="trailingIcon" :class="ui.trailingIcon({ class: props.ui?.trailingIcon })" />
        </slot>
      </span>
    </Select.Trigger>

    <Select.Portal v-bind="portalProps" :disabled="disabled">
      <Select.Content v-bind="contentProps" :class="ui.content({ class: props.ui?.content })">
        <slot name="content-top" />

        <Select.Viewport v-bind="viewportProps" :class="ui.viewport({ class: props.ui?.viewport })">
          <!--  -->
        </Select.Viewport>

        <slot name="content-bottom" />

        <Select.Arrow v-bind="arrowProps" :class="ui.arrow({ class: props.ui?.arrow })" />
      </Select.Content>
    </Select.Portal>
  </Select.Root>
</template>
