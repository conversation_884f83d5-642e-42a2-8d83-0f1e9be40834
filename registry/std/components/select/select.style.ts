import type { VariantProps } from '~/utils/styles'
import { createVariants } from '~/utils/styles'

export const useSelectStyle = createVariants({
  slots: {
    trigger: '',
    leading: '',
    leadingIcon: '',
    trailing: '',
    trailingIcon: '',
    value: '',
    content: '',
    viewport: '',
    group: '',
    label: '',
    item: '',
    itemText: '',
    itemLeading: '',
    itemTrailing: '',
    indicator: '',
    separator: '',
    arrow: '',
    empty: '',
  },

  variants: {
    color: { accent: '', neutral: '', info: '', success: '', warning: '', error: '' },
    variant: {
      normal: {},
      outline: {},
      none: '',
    },
    radius: {
      none: '',
      xs: '',
      sm: '',
      md: '',
      lg: '',
      xl: '',
    },
    size: {
      xs: '',
      sm: '',
      md: '',
      lg: '',
    },
  },

  compoundVariants: [],

  defaultVariants: {
    color: 'accent',
    variant: 'normal',
    radius: 'sm',
    size: 'md',
  },
})

export type SelectStyleSlots = typeof useSelectStyle['slots']
export type SelectStyleProps = VariantProps<typeof useSelectStyle>
