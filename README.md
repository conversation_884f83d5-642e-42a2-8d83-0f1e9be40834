<p align="center">
  <img align="center" src="https://raw.githubusercontent.com/moujinet/assets/main/weme-ui/png/circle-128.png" height="128" />
  <h1 align="center">
    Weme UI
  </h1>
</p>

[![npm version][npm-version-src]][npm-version-href]
[![License][license-src]][license-href]

<p align="center">
  Re-usable UI components with Reka UI and UnoCSS.
</p>

## 特性

- 🌈 [Colors](https://github.com/weme-ui/weme-ui/packages/colors) - 基于 Radix Colors 并支持自定义颜色。
- 🎨 [UnoCSS Preset](https://github.com/weme-ui/weme-ui/packages/unocss-preset) - 提供 UnoCSS Preset 来简化样式编写。
- ⚒️ [CLI 工具](https://github.com/weme-ui/weme-ui/packages/cli) - 提供 CLI 工具来初始化项目，添加组件，创建主题等。

---

## 如何开始

### 初始化

```bash
pnpm dlx @weme-ui/weme-ui init
```

### 添加组件

```bash
pnpm dlx @weme-ui/weme-ui add <component-name>
```

### 创建主题

```bash
pnpm dlx @weme-ui/weme-ui theme <theme-name>
```

---

## 鸣谢

- [Nuxt][nuxt-href]
- [UnoCSS][unocss-href]
- [Reka UI][reka-href]
- [Radix UI][radix-href]
- [shadcn/ui][shadcn-href]

## 许可证

[MIT][license-href] License © 2025 [weme-ui][github-href]

[npm-version-src]: https://img.shields.io/npm/v/@weme-ui/weme-ui?style=flat&colorA=1d2129&colorB=4CBBA5
[npm-version-href]: https://npmjs.com/package/@weme-ui/weme-ui
[license-src]: https://img.shields.io/github/license/weme-ui/weme-ui.svg?style=flat&colorA=1d2129&colorB=4CBBA5
[license-href]: https://github.com/weme-ui/weme-ui/blob/main/LICENSE
[github-href]: https://github.com/weme-ui/weme-ui
[nuxt-href]: https://nuxt.com
[unocss-href]: https://unocss.dev
[reka-href]: https://reka-ui.com
[radix-href]: https://www.radix-ui.com
[shadcn-href]: https://ui.shadcn.com
